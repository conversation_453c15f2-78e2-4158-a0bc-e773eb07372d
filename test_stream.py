#!/usr/bin/env python3
"""
测试流式接口的脚本
"""
import asyncio
import aiohttp
import json

async def test_stream_api():
    """测试流式API"""
    task_id = "test_stream_task"
    url = f"http://localhost:8000/api/tasks/{task_id}/messages"
    
    data = {
        "content": "请生成一个简单的HTML报告，包含标题和一些内容",
        "context_html": ""
    }
    
    print(f"发送请求到: {url}")
    print(f"请求数据: {data}")
    print("-" * 50)
    
    async with aiohttp.ClientSession() as session:
        async with session.post(url, json=data) as response:
            print(f"响应状态: {response.status}")
            print(f"响应头: {dict(response.headers)}")
            print("-" * 50)
            
            if response.status != 200:
                error_text = await response.text()
                print(f"错误响应: {error_text}")
                return
            
            # 读取流式响应
            chunk_count = 0
            async for line in response.content:
                line_str = line.decode('utf-8').strip()
                if line_str.startswith('data: '):
                    chunk_count += 1
                    data_str = line_str[6:]  # 移除 "data: " 前缀
                    
                    try:
                        chunk_data = json.loads(data_str)
                        print(f"Chunk {chunk_count}: {chunk_data}")
                        
                        # 如果是结束信号，退出循环
                        if chunk_data.get('type') == 'end':
                            print("收到结束信号")
                            break
                            
                    except json.JSONDecodeError as e:
                        print(f"JSON解析错误: {e}, 原始数据: {data_str}")
                        
            print(f"\n总共收到 {chunk_count} 个数据块")

if __name__ == "__main__":
    asyncio.run(test_stream_api())
