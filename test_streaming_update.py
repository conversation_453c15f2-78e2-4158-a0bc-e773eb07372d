#!/usr/bin/env python3
"""
测试新的流式消息处理功能
"""
import urllib.request
import urllib.parse
import json
import re

def test_streaming_message():
    """测试流式消息处理"""
    
    # 测试数据
    task_id = "test_streaming_task"
    test_message = "请为我生成一个包含图表的数据分析报告"
    
    url = f"http://localhost:8000/api/tasks/{task_id}/messages"
    data = {
        "content": test_message,
        "context_html": ""
    }
    
    print(f"发送测试消息到: {url}")
    print(f"消息内容: {test_message}")
    print("-" * 60)
    
    try:
        # 准备请求数据
        json_data = json.dumps(data).encode('utf-8')
        req = urllib.request.Request(url, data=json_data)
        req.add_header('Content-Type', 'application/json')

        # 发送请求
        response = urllib.request.urlopen(req)
        print(f"响应状态: {response.status}")

        if response.status != 200:
            error_text = response.read().decode('utf-8')
            print(f"错误响应: {error_text}")
            return

        # 读取流式响应
        chunk_count = 0
        assistant_chunks = []
        has_code_snippet = False

        for line in response:
            line_str = line.decode('utf-8').strip()
            if line_str and line_str.startswith('data: '):
                chunk_count += 1
                data_str = line_str[6:]  # 移除 "data: " 前缀
                    
                try:
                    chunk_data = json.loads(data_str)
                    chunk_type = chunk_data.get('type', 'unknown')
                    content = chunk_data.get('content', '')

                    print(f"Chunk {chunk_count}: [{chunk_type}] {content[:100]}...")

                    # 收集assistant_chunk类型的数据
                    if chunk_type == 'assistant_chunk':
                        assistant_chunks.append(content)
                        if '<code_snippet>' in content or '</code_snippet>' in content:
                            has_code_snippet = True

                    # 如果是结束信号，退出循环
                    if chunk_type == 'end':
                        print("收到结束信号")
                        break

                except json.JSONDecodeError as e:
                    print(f"JSON解析错误: {e}, 原始数据: {data_str}")
            
            print(f"\n总共收到 {chunk_count} 个数据块")
            print(f"Assistant chunks: {len(assistant_chunks)}")
            print(f"包含代码片段: {has_code_snippet}")
            
            # 合并所有assistant chunks
            full_response = ''.join(assistant_chunks)
            print(f"\n完整响应长度: {len(full_response)} 字符")
            
        # 检查代码片段处理
        if has_code_snippet:
            print("\n检测到代码片段标签:")
            if '<code_snippet>' in full_response:
                start_idx = full_response.find('<code_snippet>')
                end_idx = full_response.find('</code_snippet>')
                if end_idx > start_idx:
                    code_content = full_response[start_idx:end_idx + len('</code_snippet>')]
                    print(f"代码片段内容: {code_content[:200]}...")

            # 测试清理后的内容
            cleaned_response = re.sub(r'<code_snippet>.*?</code_snippet>', '', full_response, flags=re.DOTALL)
            cleaned_response = re.sub(r'\n\s*\n\s*\n', '\n\n', cleaned_response).strip()
            print(f"清理后响应长度: {len(cleaned_response)} 字符")
            print(f"清理后内容预览: {cleaned_response[:200]}...")

    except Exception as e:
        print(f"请求失败: {e}")
    
    # 验证消息是否正确保存
    print("\n" + "="*60)
    print("验证消息保存...")
    
    try:
        response = urllib.request.urlopen(f"http://localhost:8000/api/tasks/{task_id}")
        if response.status == 200:
            task_data = json.loads(response.read().decode('utf-8'))
            messages = task_data.get('data', {}).get('messages', [])

            print(f"任务中保存的消息数量: {len(messages)}")

            for i, msg in enumerate(messages):
                role = msg.get('role', 'unknown')
                content = msg.get('content', '')
                has_code = msg.get('has_code_snippet', False)

                print(f"消息 {i+1}: [{role}] 长度={len(content)}, 包含代码片段={has_code}")

                if role == 'assistant':
                    # 检查保存的assistant消息是否已经清理了代码片段
                    if '<code_snippet>' in content:
                        print("  ❌ 警告: 保存的消息仍包含代码片段标签!")
                    else:
                        print("  ✅ 保存的消息已正确清理代码片段标签")

                    print(f"  内容预览: {content[:150]}...")
        else:
            print(f"获取任务失败: {response.status}")
    except Exception as e:
        print(f"验证消息保存失败: {e}")

def main():
    """主函数"""
    print("开始测试新的流式消息处理功能...")
    print("="*60)

    try:
        test_streaming_message()
        print("\n测试完成!")
    except Exception as e:
        print(f"\n测试失败: {e}")

if __name__ == "__main__":
    main()
