#!/usr/bin/env python3
"""
简单的流式接口测试脚本
使用 requests 库测试流式响应
"""
import requests
import json
import time

def test_streaming_api():
    """测试流式API"""
    task_id = "test_stream_task_simple"
    url = f"http://localhost:8000/api/tasks/{task_id}/messages"
    
    data = {
        "content": "请生成一个简单的HTML报告，包含标题和一些内容",
        "context_html": ""
    }
    
    print(f"发送请求到: {url}")
    print(f"请求数据: {data}")
    print("-" * 50)
    
    try:
        response = requests.post(url, json=data, stream=True)
        print(f"响应状态: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print("-" * 50)
        
        if response.status_code != 200:
            print(f"错误响应: {response.text}")
            return
        
        # 读取流式响应
        chunk_count = 0
        for line in response.iter_lines(decode_unicode=True):
            if line and line.startswith('data: '):
                chunk_count += 1
                data_str = line[6:]  # 移除 "data: " 前缀
                
                try:
                    chunk_data = json.loads(data_str)
                    print(f"Chunk {chunk_count}: {chunk_data['type']} - {chunk_data['content'][:100]}...")
                    
                    if chunk_data.get('progress') is not None:
                        print(f"  进度: {chunk_data['progress']}%")
                    
                    # 如果是结束信号，退出循环
                    if chunk_data.get('type') == 'end':
                        print("收到结束信号")
                        break
                        
                except json.JSONDecodeError as e:
                    print(f"JSON解析错误: {e}, 原始数据: {data_str}")
                    
        print(f"\n总共收到 {chunk_count} 个数据块")
        
    except requests.exceptions.RequestException as e:
        print(f"请求错误: {e}")
    except KeyboardInterrupt:
        print("\n用户中断测试")

if __name__ == "__main__":
    test_streaming_api()
