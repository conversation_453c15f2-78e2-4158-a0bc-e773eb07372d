<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>大模型产业发展报告 - 测试</title>
    <link href="https://unpkg.com/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://unpkg.com/echarts@5.6.0/dist/echarts.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
            color: #2d3748;
            line-height: 1.6;
        }
        .header-gradient {
            background: linear-gradient(135deg, #1e3a8a 0%, #0284c7 100%);
        }
    </style>
</head>
<body>
    <!-- 报告封面 -->
    <div class="header-gradient min-h-screen flex flex-col items-center justify-center text-white py-20 px-4">
        <h1 class="text-5xl md:text-6xl font-bold mb-6 text-center">大模型产业发展报告</h1>
        <p class="text-xl md:text-2xl font-light mb-8 text-center max-w-3xl leading-relaxed">深度解析大模型技术变革与产业生态演进趋势</p>
        <p class="mt-16 text-blue-100">报告日期: 2024年12月</p>
    </div>

    <!-- 报告正文 -->
    <div class="px-4 sm:px-6 py-16 max-w-4xl mx-auto">
        <section class="mb-16">
            <h1 class="text-3xl font-bold mb-6">一、大模型技术发展现状</h1>
            
            <p class="mt-6 mb-4">
                大语言模型（Large Language Model，LLM）作为人工智能领域的重要突破，正在重塑整个AI产业格局。
                自2017年<span class="font-bold">Transformer架构</span>问世以来，大模型技术经历了从GPT-1的1.17亿参数到GPT-4的万亿级参数的跨越式发展。
            </p>

            <div class="bg-white p-6 rounded-xl shadow-md mb-6">
                <h3 class="text-xl font-semibold mb-3">模型规模演进</h3>
                <div id="model-scale-chart" style="width: 100%; height: 400px;"></div>
            </div>

            <h2 class="text-2xl font-bold mt-8 mb-4 border-b border-blue-200 pb-2">1.1 技术架构发展</h2>
            <p class="mb-4">当前大模型主要基于Transformer架构的三种变体：</p>
            <ul class="list-disc pl-6 my-4 space-y-2">
                <li><strong>编码器-解码器架构</strong>：如T5、BART，适用于文本生成和翻译任务</li>
                <li><strong>仅编码器架构</strong>：如BERT、RoBERTa，专注于文本理解任务</li>
                <li><strong>仅解码器架构</strong>：如GPT系列、LLaMA，主导当前生成式AI应用</li>
            </ul>
        </section>

        <section class="mb-16">
            <h1 class="text-3xl font-bold mb-6">二、产业应用生态</h1>
            
            <p class="mt-6 mb-4">
                大模型技术正在各个垂直领域实现深度渗透，从<span class="font-bold">内容创作</span>、<span class="font-bold">代码生成</span>到<span class="font-bold">智能客服</span>、<span class="font-bold">教育培训</span>，应用场景日益丰富。
            </p>

            <div class="bg-white p-6 rounded-xl shadow-md mb-6">
                <h3 class="text-xl font-semibold mb-3">应用领域分布</h3>
                <div id="application-pie-chart" style="width: 100%; height: 400px;"></div>
            </div>
        </section>
    </div>

    <!-- 页脚 -->
    <footer class="bg-blue-800 text-white py-8">
        <div class="max-w-7xl mx-auto px-4 text-center">
            <p class="mb-1">Created by report agent</p>
            <p class="text-blue-200 text-sm">页面内容均由 AI 生成，仅供参考</p>
        </div>
    </footer>

    <script>
        // 初始化模型规模演进图表
        const modelScaleChart = echarts.init(document.getElementById('model-scale-chart'));
        modelScaleChart.setOption({
            title: { text: '主流大模型参数规模演进', left: 'center' },
            tooltip: { 
                trigger: 'axis',
                formatter: function(params) {
                    return params[0].name + '<br/>' + 
                           params[0].seriesName + ': ' + params[0].value + '亿参数';
                }
            },
            legend: {
                data: ['GPT系列', 'PaLM系列', 'LLaMA系列', '国产模型'],
                bottom: 10
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '15%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: ['2018', '2019', '2020', '2021', '2022', '2023', '2024']
            },
            yAxis: { 
                type: 'log',
                name: '参数量（亿）',
                axisLabel: {
                    formatter: '{value}'
                }
            },
            series: [
                {
                    name: 'GPT系列',
                    type: 'line',
                    data: [1.17, 15, 175, 175, 175, 1000, 1000],
                    smooth: true,
                    lineStyle: { width: 3, color: '#10b981' }
                },
                {
                    name: 'PaLM系列',
                    type: 'line',
                    data: [null, null, null, null, 5400, 5400, 5400],
                    smooth: true,
                    lineStyle: { width: 3, color: '#f59e0b' }
                },
                {
                    name: 'LLaMA系列',
                    type: 'line',
                    data: [null, null, null, null, null, 650, 4050],
                    smooth: true,
                    lineStyle: { width: 3, color: '#8b5cf6' }
                },
                {
                    name: '国产模型',
                    type: 'line',
                    data: [null, null, null, null, 26, 340, 720],
                    smooth: true,
                    lineStyle: { width: 3, color: '#ef4444' }
                }
            ]
        });

        // 初始化应用领域饼图
        const applicationPieChart = echarts.init(document.getElementById('application-pie-chart'));
        applicationPieChart.setOption({
            title: { text: '大模型应用领域分布', left: 'center' },
            tooltip: {
                trigger: 'item',
                formatter: '{b}: {c}% ({d}%)'
            },
            legend: {
                orient: 'horizontal',
                bottom: 10
            },
            series: [
                {
                    type: 'pie',
                    radius: ['30%', '70%'],
                    center: ['50%', '45%'],
                    data: [
                        { value: 26.7, name: '内容创作', itemStyle: { color: '#10b981' } },
                        { value: 21.1, name: '代码生成', itemStyle: { color: '#3b82f6' } },
                        { value: 17.8, name: '智能客服', itemStyle: { color: '#f59e0b' } },
                        { value: 12.2, name: '教育培训', itemStyle: { color: '#8b5cf6' } },
                        { value: 10.0, name: '数据分析', itemStyle: { color: '#ef4444' } },
                        { value: 7.8, name: '翻译服务', itemStyle: { color: '#06b6d4' } },
                        { value: 4.4, name: '其他', itemStyle: { color: '#64748b' } }
                    ],
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    },
                    label: {
                        formatter: '{b}\n{c}%'
                    }
                }
            ]
        });

        // 响应式调整
        window.addEventListener('resize', function() {
            modelScaleChart.resize();
            applicationPieChart.resize();
        });
    </script>
</body>
</html>
