[project]
name = "bestreport-backend"
version = "1.0.0"
description = "BestReport Backend API - 智能数据报告生成系统后端服务"
authors = [
    {name = "BestReport Team", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.9"
dependencies = [
    "fastapi==0.104.1",
    "uvicorn[standard]==0.24.0",
    "websockets==12.0",
    "aiosqlite==0.19.0",
    "sqlalchemy==2.0.23",
    "pydantic==2.5.0",
    "pydantic-settings==2.1.0",
    "python-multipart==0.0.6",
    "httpx==0.25.2",
    "python-dotenv==1.0.0",
    "bs4>=0.0.2",
]

[project.optional-dependencies]
dev = [
    "pytest==7.4.3",
    "pytest-asyncio==0.21.1",
    "httpx==0.25.2",
    "websockets==12.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["app"]

[tool.uv]
dev-dependencies = [
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
]


