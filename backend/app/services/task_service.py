"""
任务相关业务逻辑
"""
import uuid
from datetime import datetime, timezone
from typing import List, Optional
from pathlib import Path

from app.core.database import execute_query, execute_one, execute_update
from app.models.schemas import Task, TaskSummary, TaskDetail, Message, TaskStatus


async def create_task(title: str, task_id: str = None) -> Task:
    """创建新任务"""
    if task_id is None:
        task_id = str(uuid.uuid4())
    now = datetime.now(timezone.utc)

    await execute_update(
        "INSERT INTO tasks (id, title, created_at, updated_at, last_activity) VALUES (?, ?, ?, ?, ?)",
        [task_id, title, now, now, now]
    )

    return await get_task_by_id(task_id)


async def get_task_by_id(task_id: str) -> Optional[Task]:
    """根据ID获取任务"""
    result = await execute_one(
        "SELECT * FROM tasks WHERE id = ?",
        [task_id]
    )
    
    if result:
        return Task(**result)
    return None


async def get_or_create_task(task_id: str) -> Task:
    """获取或创建任务"""
    task = await get_task_by_id(task_id)
    if not task:
        task = await create_task(f"Report Task {task_id[:8]}", task_id)
    return task


async def update_task_report_path(task_id: str, report_path: str) -> None:
    """更新任务的报告文件路径"""
    await execute_update(
        "UPDATE tasks SET report_file_path = ?, updated_at = ? WHERE id = ?",
        [report_path, datetime.now(timezone.utc), task_id]
    )


async def update_task_status(task_id: str, status: TaskStatus, progress: int = None, error_message: str = None) -> None:
    """更新任务状态"""
    now = datetime.now(timezone.utc)

    if progress is not None:
        await execute_update(
            "UPDATE tasks SET status = ?, progress = ?, error_message = ?, last_activity = ?, updated_at = ? WHERE id = ?",
            [status.value, progress, error_message, now, now, task_id]
        )
    else:
        await execute_update(
            "UPDATE tasks SET status = ?, error_message = ?, last_activity = ?, updated_at = ? WHERE id = ?",
            [status.value, error_message, now, now, task_id]
        )


async def get_task_status(task_id: str) -> Optional[dict]:
    """获取任务状态"""
    result = await execute_one(
        "SELECT id, status, progress, error_message, last_activity FROM tasks WHERE id = ?",
        [task_id]
    )
    return result


async def reset_task_status(task_id: str) -> None:
    """重置任务状态为空闲"""
    await update_task_status(task_id, TaskStatus.IDLE, 0, None)


async def get_or_create_task(task_id: str, title: str = None) -> Task:
    """获取或创建任务"""
    # 先尝试获取现有任务
    task = await get_task_by_id(task_id)

    if task:
        return task

    # 如果任务不存在，创建新任务
    if not title:
        title = f"任务 {task_id}"

    await create_task(title, task_id)
    task = await get_task_by_id(task_id)

    if not task:
        raise Exception(f"Failed to create task {task_id}")

    return task


async def get_tasks(limit: int = 20, offset: int = 0) -> List[TaskSummary]:
    """获取任务列表"""
    # 获取任务基础信息
    tasks = await execute_query(
        "SELECT * FROM tasks ORDER BY updated_at DESC LIMIT ? OFFSET ?",
        [limit, offset]
    )
    
    # 为每个任务添加消息计数
    result = []
    for task in tasks:
        message_count = await execute_one(
            "SELECT COUNT(*) as count FROM messages WHERE task_id = ?",
            [task["id"]]
        )
        
        task_summary = TaskSummary(
            id=task["id"],
            title=task["title"],
            created_at=task["created_at"],
            updated_at=task["updated_at"],
            message_count=message_count["count"] if message_count else 0
        )
        result.append(task_summary)
    
    return result


async def get_task_detail(task_id: str) -> Optional[TaskDetail]:
    """获取任务详情"""
    # 获取任务信息
    task_data = await execute_one(
        "SELECT * FROM tasks WHERE id = ?",
        [task_id]
    )
    
    if not task_data:
        return None
    
    # 获取消息历史
    messages_data = await execute_query(
        "SELECT * FROM messages WHERE task_id = ? ORDER BY timestamp ASC",
        [task_id]
    )
    
    task = Task(**task_data)
    messages = [Message(**msg) for msg in messages_data]
    
    return TaskDetail(task=task, messages=messages)


async def delete_task(task_id: str) -> bool:
    """删除任务及其相关数据"""
    # 检查任务是否存在
    task = await get_task_by_id(task_id)
    if not task:
        return False

    # 删除任务相关的消息
    await execute_update(
        "DELETE FROM messages WHERE task_id = ?",
        [task_id]
    )

    # 删除任务记录
    await execute_update(
        "DELETE FROM tasks WHERE id = ?",
        [task_id]
    )

    # 删除报告文件（如果存在）
    if task.report_file_path:
        try:
            import shutil
            task_dir = Path(task.report_file_path)
            if task_dir.exists() and task_dir.is_dir():
                shutil.rmtree(task_dir)
        except Exception as e:
            # 文件删除失败不影响数据库删除
            print(f"Warning: Failed to delete report files for task {task_id}: {e}")

    return True
