"""
消息相关业务逻辑
"""
import uuid
from datetime import datetime, timezone
from typing import List

from app.core.database import execute_update, execute_query
from app.models.schemas import Message


async def save_message(
    task_id: str, 
    role: str, 
    content: str, 
    has_code_snippet: bool = False
) -> str:
    """保存消息到数据库"""
    message_id = str(uuid.uuid4())
    now = datetime.now(timezone.utc)
    
    await execute_update(
        "INSERT INTO messages (id, task_id, role, content, timestamp, has_code_snippet) VALUES (?, ?, ?, ?, ?, ?)",
        [message_id, task_id, role, content, now, has_code_snippet]
    )
    
    # 更新任务的最后更新时间
    await execute_update(
        "UPDATE tasks SET updated_at = ? WHERE id = ?",
        [now, task_id]
    )
    
    return message_id


async def get_task_messages(task_id: str, limit: int = 50) -> List[Message]:
    """获取任务的消息列表"""
    results = await execute_query(
        "SELECT * FROM messages WHERE task_id = ? ORDER BY timestamp DESC LIMIT ?",
        [task_id, limit]
    )

    return [Message(**result) for result in results]
