"""
异步任务处理服务
"""
import asyncio
import json
from datetime import datetime, timezone
from typing import Optional, Dict, Any
from concurrent.futures import ThreadPoolExecutor

from app.services.task_service import update_task_status, get_task_by_id
from app.services.message_service import save_message, get_task_messages
from app.services.llm_service import generate_llm_response
from app.services.report_service import update_report_content, get_report_content
from app.services.html_diff_service import apply_html_diff, extract_modifications_from_response
from app.models.schemas import TaskStatus


class AsyncTaskProcessor:
    """异步任务处理器"""
    
    def __init__(self):
        self.executor = ThreadPoolExecutor(max_workers=4)
        self.running_tasks: Dict[str, asyncio.Task] = {}
    
    async def process_message(self, task_id: str, user_message: str, context_html: str = "") -> str:
        """处理用户消息，异步生成响应"""
        
        # 检查是否已有任务在运行
        if task_id in self.running_tasks and not self.running_tasks[task_id].done():
            raise ValueError(f"Task {task_id} is already processing")
        
        # 创建异步任务
        task = asyncio.create_task(self._process_message_async(task_id, user_message, context_html))
        self.running_tasks[task_id] = task
        
        return f"Task {task_id} started processing"
    
    async def _process_message_async(self, task_id: str, user_message: str, context_html: str):
        """异步处理消息的内部方法"""
        try:
            # 确保任务存在
            from app.services.task_service import get_or_create_task
            task = await get_or_create_task(task_id)

            # 更新任务状态为处理中
            await update_task_status(task_id, TaskStatus.PROCESSING, 0)

            # 保存用户消息
            await save_message(task_id, "user", user_message)
            
            # 获取报告内容
            report = await get_report_content(task_id)
            
            # 生成LLM响应
            full_response = ""
            text_response = ""
            has_code_snippet = False
            
            # 更新进度
            await update_task_status(task_id, TaskStatus.PROCESSING, 10)
            
            async for chunk in generate_llm_response(task_id, user_message, context_html, report):
                full_response += chunk
                
                # 检查是否包含代码片段
                if "<code_snippet>" in chunk or "</code_snippet>" in chunk:
                    has_code_snippet = True
                
                # 更新进度（模拟）
                progress = min(90, len(full_response) // 10)
                await update_task_status(task_id, TaskStatus.PROCESSING, progress)
            
            # 处理代码片段
            if has_code_snippet:
                await self._process_code_snippets(task_id, full_response)
            
            # 保存助手响应
            await save_message(task_id, "assistant", full_response, has_code_snippet)
            
            # 更新任务状态为完成
            await update_task_status(task_id, TaskStatus.COMPLETED, 100)
            
        except Exception as e:
            # 更新任务状态为错误
            await update_task_status(task_id, TaskStatus.ERROR, 0, str(e))
            print(f"[ERROR] AsyncTaskProcessor: Failed to process message for task {task_id}: {e}")
        finally:
            # 清理任务引用
            if task_id in self.running_tasks:
                del self.running_tasks[task_id]
    
    async def _process_code_snippets(self, task_id: str, response: str):
        """处理响应中的代码片段"""
        try:
            # 提取修改内容
            modifications = extract_modifications_from_response(response)
            
            if modifications:
                # 获取当前报告内容
                current_report = await get_report_content(task_id)
                current_html = current_report.content if current_report else ""
                
                # 应用修改
                updated_html = apply_html_diff(current_html, modifications)
                
                # 更新报告内容
                await update_report_content(task_id, updated_html)
                
        except Exception as e:
            print(f"[ERROR] AsyncTaskProcessor: Failed to process code snippets for task {task_id}: {e}")
    
    def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """获取任务处理状态"""
        if task_id in self.running_tasks:
            task = self.running_tasks[task_id]
            return {
                "is_running": not task.done(),
                "is_cancelled": task.cancelled(),
                "exception": str(task.exception()) if task.done() and task.exception() else None
            }
        return {"is_running": False, "is_cancelled": False, "exception": None}
    
    async def cancel_task(self, task_id: str) -> bool:
        """取消任务处理"""
        if task_id in self.running_tasks:
            task = self.running_tasks[task_id]
            if not task.done():
                task.cancel()
                await update_task_status(task_id, TaskStatus.IDLE, 0)
                return True
        return False
    
    def cleanup(self):
        """清理资源"""
        for task in self.running_tasks.values():
            if not task.done():
                task.cancel()
        self.running_tasks.clear()
        self.executor.shutdown(wait=True)


# 全局异步任务处理器实例
async_task_processor = AsyncTaskProcessor()
