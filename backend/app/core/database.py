"""
数据库连接和初始化
"""
import aiosqlite
from pathlib import Path
from typing import AsyncGenerator
from contextlib import asynccontextmanager

from app.core.config import get_data_dir


DATABASE_PATH = get_data_dir() / "bestreport.db"


@asynccontextmanager
async def get_database() -> AsyncGenerator[aiosqlite.Connection, None]:
    """获取数据库连接"""
    async with aiosqlite.connect(DATABASE_PATH) as db:
        # 启用外键约束
        await db.execute("PRAGMA foreign_keys = ON")
        yield db


async def init_database():
    """初始化数据库表结构"""
    async with get_database() as db:
        # 创建任务表
        await db.execute("""
            CREATE TABLE IF NOT EXISTS tasks (
                id TEXT PRIMARY KEY,
                title TEXT NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                report_file_path TEXT,
                report_html_filename TEXT DEFAULT 'report.html',
                status TEXT DEFAULT 'idle' CHECK(status IN ('idle', 'processing', 'completed', 'error')),
                progress INTEGER DEFAULT 0,
                error_message TEXT,
                last_activity DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 创建消息表
        await db.execute("""
            CREATE TABLE IF NOT EXISTS messages (
                id TEXT PRIMARY KEY,
                task_id TEXT NOT NULL,
                role TEXT CHECK(role IN ('user', 'assistant')) NOT NULL,
                content TEXT NOT NULL,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                has_code_snippet BOOLEAN DEFAULT FALSE,
                FOREIGN KEY (task_id) REFERENCES tasks (id) ON DELETE CASCADE
            )
        """)
        
        # 创建配置表
        await db.execute("""
            CREATE TABLE IF NOT EXISTS config (
                key TEXT PRIMARY KEY,
                value TEXT NOT NULL,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 创建索引
        await db.execute("CREATE INDEX IF NOT EXISTS idx_messages_task_id ON messages(task_id)")
        await db.execute("CREATE INDEX IF NOT EXISTS idx_messages_timestamp ON messages(timestamp)")
        await db.execute("CREATE INDEX IF NOT EXISTS idx_tasks_updated_at ON tasks(updated_at)")
        
        await db.commit()

    # 执行数据库迁移
    await migrate_database()


async def migrate_database():
    """迁移数据库结构"""
    async with get_database() as db:
        # 检查是否需要添加新字段
        cursor = await db.execute("PRAGMA table_info(tasks)")
        columns = await cursor.fetchall()
        column_names = [col[1] for col in columns]

        # 添加缺失的字段
        if 'status' not in column_names:
            await db.execute("ALTER TABLE tasks ADD COLUMN status TEXT DEFAULT 'idle' CHECK(status IN ('idle', 'processing', 'completed', 'error'))")

        if 'progress' not in column_names:
            await db.execute("ALTER TABLE tasks ADD COLUMN progress INTEGER DEFAULT 0")

        if 'error_message' not in column_names:
            await db.execute("ALTER TABLE tasks ADD COLUMN error_message TEXT")

        if 'last_activity' not in column_names:
            await db.execute("ALTER TABLE tasks ADD COLUMN last_activity DATETIME")
            # 为现有记录设置默认值
            await db.execute("UPDATE tasks SET last_activity = created_at WHERE last_activity IS NULL")

        await db.commit()


async def execute_query(query: str, params: list = None) -> list:
    """执行查询并返回结果"""
    async with get_database() as db:
        if params:
            cursor = await db.execute(query, params)
        else:
            cursor = await db.execute(query)
        
        rows = await cursor.fetchall()
        # 获取列名
        columns = [description[0] for description in cursor.description]
        
        # 转换为字典列表
        result = []
        for row in rows:
            result.append(dict(zip(columns, row)))
        
        return result


async def execute_one(query: str, params: list = None) -> dict:
    """执行查询并返回单个结果"""
    results = await execute_query(query, params)
    return results[0] if results else None


async def execute_update(query: str, params: list = None) -> int:
    """执行更新操作并返回影响的行数"""
    async with get_database() as db:
        if params:
            cursor = await db.execute(query, params)
        else:
            cursor = await db.execute(query)
        
        await db.commit()
        return cursor.rowcount
