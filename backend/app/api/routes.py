"""
REST API路由
"""
from typing import List
from fastapi import APIRouter, HTTPException, Query
from fastapi.responses import StreamingResponse
from pydantic import ValidationError
import json
import asyncio
import re

from app.models.schemas import (
    TaskSummary, TaskDetail, Config, ConfigUpdate,
    ReportContentRequest, ReportContentResponse,
    ErrorResponse, MessageRequest, TaskStatusResponse, MessageResponse
)
from app.services.task_service import get_tasks, get_task_detail, delete_task, get_task_status
from app.services.config_service import get_config, update_config
from app.services.report_service import get_report_content, update_report_content, backup_report_content, list_report_backups
from app.services.message_service import get_task_messages


api_router = APIRouter()


def _clean_response_content(response: str) -> str:
    """清理响应内容，移除代码片段标签及其内容"""
    # 移除 <code_snippet>...</code_snippet> 标签及其内容
    cleaned = re.sub(r'<code_snippet>.*?</code_snippet>', '', response, flags=re.DOTALL)
    # 清理多余的空行
    cleaned = re.sub(r'\n\s*\n\s*\n', '\n\n', cleaned)
    return cleaned.strip()


async def _process_message_stream(task_id: str, user_message: str, context_html: str = ""):
    """处理消息并生成流式响应"""
    from app.services.task_service import get_or_create_task, update_task_status
    from app.services.message_service import save_message
    from app.services.report_service import get_report_content, update_report_content
    from app.services.llm_service import generate_llm_response
    from app.services.html_diff_service import apply_html_diff, extract_modifications_from_response
    from app.models.schemas import TaskStatus

    try:
        # 发送开始信号
        yield {
            "type": "start",
            "content": "开始处理消息...",
            "timestamp": asyncio.get_event_loop().time()
        }

        # 确保任务存在
        task = await get_or_create_task(task_id)

        # 更新任务状态为处理中
        await update_task_status(task_id, TaskStatus.PROCESSING, 0)

        # 发送状态更新
        yield {
            "type": "status",
            "content": "正在保存用户消息...",
            "progress": 5,
            "timestamp": asyncio.get_event_loop().time()
        }

        # 保存用户消息
        await save_message(task_id, "user", user_message)

        # 获取报告内容
        report = await get_report_content(task_id)

        # 发送状态更新
        yield {
            "type": "status",
            "content": "正在生成AI响应...",
            "progress": 10,
            "timestamp": asyncio.get_event_loop().time()
        }

        # 生成LLM响应
        full_response = ""
        has_code_snippet = False

        async for chunk in generate_llm_response(task_id, user_message, context_html, report):
            full_response += chunk

            # 检查是否包含代码片段
            if "<code_snippet>" in full_response and "</code_snippet>" in full_response:
                has_code_snippet = True

            # 发送响应块
            yield {
                "type": "assistant_chunk",
                "content": chunk,
                "has_code_snippet": has_code_snippet,
                "timestamp": asyncio.get_event_loop().time()
            }

        # 发送状态更新
        yield {
            "type": "status",
            "content": "正在处理代码片段...",
            "progress": 80,
            "timestamp": asyncio.get_event_loop().time()
        }

        # 处理代码片段
        if has_code_snippet:
            try:
                # 提取修改内容
                modifications = extract_modifications_from_response(full_response)
                print(f"[DEBUG] Extracted modifications: {modifications}")

                if modifications:
                    # 获取当前报告内容
                    current_report = await get_report_content(task_id)
                    current_html = current_report.content if current_report else ""

                    # 应用修改
                    updated_html = apply_html_diff(current_html, modifications)

                    # 更新报告内容
                    await update_report_content(task_id, updated_html)

                    yield {
                        "type": "code_processed",
                        "content": "代码片段已处理并更新到报告",
                        "timestamp": asyncio.get_event_loop().time()
                    }

            except Exception as e:
                yield {
                    "type": "warning",
                    "content": f"处理代码片段时出现警告: {str(e)}",
                    "timestamp": asyncio.get_event_loop().time()
                }

        # 发送状态更新
        yield {
            "type": "status",
            "content": "正在保存响应...",
            "progress": 90,
            "timestamp": asyncio.get_event_loop().time()
        }

        # 过滤掉代码片段内容后保存助手响应
        clean_response = _clean_response_content(full_response)
        await save_message(task_id, "assistant", clean_response, has_code_snippet)

        # 更新任务状态为完成
        await update_task_status(task_id, TaskStatus.COMPLETED, 100)

        # 发送完成信号
        yield {
            "type": "completed",
            "content": "消息处理完成",
            "progress": 100,
            "timestamp": asyncio.get_event_loop().time()
        }

    except Exception as e:
        # 更新任务状态为错误
        await update_task_status(task_id, TaskStatus.ERROR, 0, str(e))

        yield {
            "type": "error",
            "content": f"处理消息时发生错误: {str(e)}",
            "timestamp": asyncio.get_event_loop().time()
        }


@api_router.get("/tasks")
async def get_task_list(
    limit: int = Query(20, ge=1, le=100, description="返回数量限制"),
    offset: int = Query(0, ge=0, description="分页偏移量")
):
    """获取任务列表"""
    try:
        tasks = await get_tasks(limit=limit, offset=offset)
        
        # 转换为字典格式
        tasks_data = [task.dict() for task in tasks]
        
        return {
            "success": True,
            "data": tasks_data
        }
    
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=ErrorResponse(
                error="Failed to get tasks",
                code="DATABASE_ERROR",
                details={"message": str(e)}
            ).dict()
        )


@api_router.get("/tasks/{task_id}")
async def get_task_details(task_id: str):
    """获取任务详情"""
    try:
        # URL解码任务ID
        import urllib.parse
        decoded_task_id = urllib.parse.unquote(task_id)
        print(f"[DEBUG] Task details request - Original: {task_id}, Decoded: {decoded_task_id}")

        task_detail = await get_task_detail(decoded_task_id)
        
        if not task_detail:
            raise HTTPException(
                status_code=404,
                detail=ErrorResponse(
                    error="Task not found",
                    code="TASK_NOT_FOUND"
                ).dict()
            )
        
        return {
            "success": True,
            "data": {
                "task": task_detail.task.dict(),
                "messages": [msg.dict() for msg in task_detail.messages]
            }
        }
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=ErrorResponse(
                error="Failed to get task detail",
                code="DATABASE_ERROR",
                details={"message": str(e)}
            ).dict()
        )


@api_router.delete("/tasks/{task_id}")
async def delete_task_endpoint(task_id: str):
    """删除任务"""
    try:
        success = await delete_task(task_id)

        if not success:
            raise HTTPException(
                status_code=404,
                detail=ErrorResponse(
                    error="Task not found",
                    code="TASK_NOT_FOUND"
                ).dict()
            )

        return {
            "success": True,
            "message": "Task deleted successfully"
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=ErrorResponse(
                error="Failed to delete task",
                code="DATABASE_ERROR",
                details={"message": str(e)}
            ).dict()
        )


@api_router.get("/tasks/{task_id}/report")
async def get_task_report_content(task_id: str):
    """获取任务报告内容"""
    try:
        report_content = await get_report_content(task_id)

        if not report_content:
            raise HTTPException(
                status_code=404,
                detail=ErrorResponse(
                    error="Report not found",
                    code="REPORT_NOT_FOUND"
                ).dict()
            )

        return {
            "success": True,
            "data": report_content.dict()
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=ErrorResponse(
                error="Failed to get report content",
                code="DATABASE_ERROR",
                details={"message": str(e)}
            ).dict()
        )


@api_router.put("/tasks/{task_id}/report")
async def update_task_report_content(task_id: str, request: ReportContentRequest):
    """更新任务报告内容"""
    try:
        # 先创建备份
        backup_path = await backup_report_content(task_id)

        # 更新报告内容
        updated_content = await update_report_content(task_id, request.content)

        if not updated_content:
            raise HTTPException(
                status_code=404,
                detail=ErrorResponse(
                    error="Task not found or failed to update report",
                    code="UPDATE_FAILED"
                ).dict()
            )

        return {
            "success": True,
            "data": updated_content.dict(),
            "backup_path": backup_path
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=ErrorResponse(
                error="Failed to update report content",
                code="DATABASE_ERROR",
                details={"message": str(e)}
            ).dict()
        )


@api_router.get("/tasks/{task_id}/report/backups")
async def get_task_report_backups(task_id: str):
    """获取任务报告备份列表"""
    try:
        backups = await list_report_backups(task_id)

        return {
            "success": True,
            "data": {
                "backups": backups,
                "count": len(backups)
            }
        }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=ErrorResponse(
                error="Failed to get report backups",
                code="DATABASE_ERROR",
                details={"message": str(e)}
            ).dict()
        )


@api_router.get("/config")
async def get_system_config():
    """获取系统配置"""
    try:
        config = await get_config()
        
        return {
            "success": True,
            "data": config.dict()
        }
    
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=ErrorResponse(
                error="Failed to get configuration",
                code="DATABASE_ERROR",
                details={"message": str(e)}
            ).dict()
        )


@api_router.put("/config")
async def update_system_config(config_update: ConfigUpdate):
    """更新系统配置"""
    try:
        await update_config(config_update)
        
        return {
            "success": True,
            "message": "Configuration updated successfully"
        }
    
    except ValidationError as e:
        raise HTTPException(
            status_code=400,
            detail=ErrorResponse(
                error="Invalid configuration",
                code="INVALID_CONFIG",
                details={"validation_errors": e.errors()}
            ).dict()
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=ErrorResponse(
                error="Failed to update configuration",
                code="DATABASE_ERROR",
                details={"message": str(e)}
            ).dict()
        )


@api_router.post("/tasks/{task_id}/messages")
async def send_message_stream(task_id: str, request: MessageRequest):
    """发送消息并返回流式响应"""
    try:
        # URL解码任务ID
        import urllib.parse
        decoded_task_id = urllib.parse.unquote(task_id)
        print(f"[DEBUG] Send message stream request - Original: {task_id}, Decoded: {decoded_task_id}")

        # 创建流式响应生成器
        async def generate_stream():
            try:
                async for chunk in _process_message_stream(decoded_task_id, request.content, request.context_html or ""):
                    yield f"data: {json.dumps(chunk, ensure_ascii=False)}\n\n"
            except Exception as e:
                error_chunk = {
                    "type": "error",
                    "content": str(e),
                    "timestamp": asyncio.get_event_loop().time()
                }
                yield f"data: {json.dumps(error_chunk, ensure_ascii=False)}\n\n"
            finally:
                # 发送结束信号
                end_chunk = {
                    "type": "end",
                    "content": "",
                    "timestamp": asyncio.get_event_loop().time()
                }
                yield f"data: {json.dumps(end_chunk, ensure_ascii=False)}\n\n"

        return StreamingResponse(
            generate_stream(),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Content-Type": "text/plain; charset=utf-8"
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=ErrorResponse(
                error="Failed to process message",
                code="PROCESSING_ERROR",
                details={"message": str(e)}
            ).dict()
        )


@api_router.get("/tasks/{task_id}/status")
async def get_task_status_endpoint(task_id: str):
    """获取任务状态"""
    try:
        # URL解码任务ID（FastAPI会自动解码，但为了确保兼容性）
        import urllib.parse
        decoded_task_id = urllib.parse.unquote(task_id)
        print(f"[DEBUG] Task status request - Original: {task_id}, Decoded: {decoded_task_id}")

        # 获取数据库中的任务状态
        status_data = await get_task_status(decoded_task_id)

        if not status_data:
            raise HTTPException(
                status_code=404,
                detail=ErrorResponse(
                    error="Task not found",
                    code="TASK_NOT_FOUND"
                ).dict()
            )
        # 获取消息数量
        messages = await get_task_messages(decoded_task_id)

        return {
            "success": True,
            "data": {
                "id": status_data["id"],
                "status": status_data["status"],
                "progress": status_data["progress"],
                "error_message": status_data["error_message"],
                "last_activity": status_data["last_activity"],
                "message_count": len(messages)
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=ErrorResponse(
                error="Failed to get task status",
                code="DATABASE_ERROR",
                details={"message": str(e)}
            ).dict()
        )


@api_router.get("/tasks/{task_id}/messages")
async def get_task_messages_endpoint(task_id: str, limit: int = Query(50, ge=1, le=100)):
    """获取任务消息列表"""
    try:
        # URL解码任务ID
        import urllib.parse
        decoded_task_id = urllib.parse.unquote(task_id)
        print(f"[DEBUG] Get messages request - Original: {task_id}, Decoded: {decoded_task_id}")

        messages = await get_task_messages(decoded_task_id, limit)

        return {
            "success": True,
            "data": [
                {
                    "id": msg.id,
                    "role": msg.role,
                    "content": msg.content,
                    "timestamp": msg.timestamp,
                    "has_code_snippet": msg.has_code_snippet
                }
                for msg in messages
            ]
        }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=ErrorResponse(
                error="Failed to get messages",
                code="DATABASE_ERROR",
                details={"message": str(e)}
            ).dict()
        )
