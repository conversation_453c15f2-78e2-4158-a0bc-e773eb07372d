#!/usr/bin/env python3
"""
测试中文URL编码问题的修复
"""
import urllib.parse


def test_chinese_task_id_encoding():
    """测试包含中文的任务ID的URL编码处理"""
    print("=== 中文任务ID URL编码修复测试 ===")
    
    # 模拟问题场景
    chinese_task_id = "任务 report_generation_test"
    print(f"原始任务ID: {chinese_task_id}")
    
    # 1. 前端编码（现在已修复）
    frontend_encoded = urllib.parse.quote(chinese_task_id, safe='')
    print(f"前端编码: {frontend_encoded}")
    
    # 2. 构建URL
    api_url = f"/api/tasks/{frontend_encoded}"
    print(f"API URL: {api_url}")
    
    # 3. 后端接收并解码（现在已修复）
    backend_decoded = urllib.parse.unquote(frontend_encoded)
    print(f"后端解码: {backend_decoded}")
    
    # 4. 验证一致性
    if chinese_task_id == backend_decoded:
        print("✅ 中文任务ID编码解码修复成功")
        return True
    else:
        print("❌ 中文任务ID编码解码仍有问题")
        return False


def test_websocket_url_encoding():
    """测试WebSocket URL中的中文编码"""
    print("\n=== WebSocket中文URL编码测试 ===")
    
    chinese_task_id = "任务 report_generation_test"
    print(f"原始任务ID: {chinese_task_id}")
    
    # WebSocket URL编码
    encoded_for_ws = urllib.parse.quote(chinese_task_id, safe='')
    ws_url = f"ws://localhost:8088/ws/chat/{encoded_for_ws}"
    print(f"WebSocket URL: {ws_url}")
    
    # WebSocket后端解码
    ws_decoded = urllib.parse.unquote(encoded_for_ws)
    print(f"WebSocket解码: {ws_decoded}")
    
    if chinese_task_id == ws_decoded:
        print("✅ WebSocket中文URL编码修复成功")
        return True
    else:
        print("❌ WebSocket中文URL编码仍有问题")
        return False


def test_api_endpoints_encoding():
    """测试各个API端点的中文编码处理"""
    print("\n=== API端点中文编码测试 ===")
    
    chinese_task_id = "任务 report_generation_test"
    encoded_id = urllib.parse.quote(chinese_task_id, safe='')
    
    # 测试各个API端点
    endpoints = [
        f"/api/tasks/{encoded_id}",
        f"/api/tasks/{encoded_id}/messages",
        f"/api/tasks/{encoded_id}/status",
        f"/api/tasks/{encoded_id}/cancel",
        f"/ws/chat/{encoded_id}",
    ]
    
    all_passed = True
    for endpoint in endpoints:
        print(f"测试端点: {endpoint}")
        
        # 提取任务ID部分并解码
        if "/ws/chat/" in endpoint:
            task_id_part = endpoint.split("/ws/chat/")[1]
        else:
            task_id_part = endpoint.split("/api/tasks/")[1].split("/")[0]
        
        decoded_id = urllib.parse.unquote(task_id_part)
        
        if decoded_id == chinese_task_id:
            print(f"  ✅ {endpoint} 编码解码正确")
        else:
            print(f"  ❌ {endpoint} 编码解码错误")
            all_passed = False
    
    return all_passed


def main():
    """主测试函数"""
    print("开始测试中文URL编码问题的修复...")
    
    test1_passed = test_chinese_task_id_encoding()
    test2_passed = test_websocket_url_encoding()
    test3_passed = test_api_endpoints_encoding()
    
    print("\n" + "="*50)
    print("修复总结:")
    print("="*50)
    
    if all([test1_passed, test2_passed, test3_passed]):
        print("🎉 所有测试通过！中文URL编码问题已修复")
        print("\n修复内容:")
        print("1. ✅ 创建了WebSocket路由 (backend/app/api/websocket.py)")
        print("2. ✅ 在WebSocket路由中添加了URL解码逻辑")
        print("3. ✅ 在所有API路由中添加了URL解码逻辑")
        print("4. ✅ 在前端API客户端中添加了URL编码逻辑")
        print("5. ✅ 注册了WebSocket路由到主应用")
        
        print("\n解决方案:")
        print("- 前端: 使用 encodeURIComponent() 编码包含中文的任务ID")
        print("- 后端: 使用 urllib.parse.unquote() 解码URL参数")
        print("- 确保前后端使用相同的UTF-8编码标准")
        
    else:
        print("❌ 部分测试失败，需要进一步检查")
        
    print("\n测试完成！")


if __name__ == "__main__":
    main()
