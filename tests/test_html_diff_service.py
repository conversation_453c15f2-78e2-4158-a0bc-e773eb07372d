"""
HTML差异服务测试
"""
import pytest
import sys
import os

# 添加backend目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'backend'))

from app.services.html_diff_service import HTMLDiffApplier, apply_html_diff, extract_modifications_from_response


class TestHTMLDiffApplier:
    """HTML差异应用器测试"""
    
    def test_simple_replace(self):
        """测试简单替换操作"""
        html = '<div><h1>原标题</h1><p>原内容</p></div>'
        modifications = '''
        {
            "action": "modify",
            "modifications": [
                {
                    "type": "replace",
                    "target": "h1",
                    "content": "<h1>新标题</h1>",
                    "description": "替换标题"
                }
            ]
        }
        '''
        
        result = apply_html_diff(html, modifications)
        assert "新标题" in result
        assert "原标题" not in result
    
    def test_insert_operation(self):
        """测试插入操作"""
        html = '<div><h1>标题</h1></div>'
        modifications = '''
        {
            "action": "modify",
            "modifications": [
                {
                    "type": "insert",
                    "target": "h1",
                    "position": "after",
                    "content": "<p>新段落</p>",
                    "description": "在标题后插入段落"
                }
            ]
        }
        '''
        
        result = apply_html_diff(html, modifications)
        assert "新段落" in result
        assert "标题" in result
    
    def test_delete_operation(self):
        """测试删除操作"""
        html = '<div><h1>标题</h1><p>要删除的段落</p></div>'
        modifications = '''
        {
            "action": "modify",
            "modifications": [
                {
                    "type": "delete",
                    "target": "p",
                    "description": "删除段落"
                }
            ]
        }
        '''
        
        result = apply_html_diff(html, modifications)
        assert "要删除的段落" not in result
        assert "标题" in result
    
    def test_update_text_content(self):
        """测试更新文本内容"""
        html = '<div><h1>旧标题</h1></div>'
        modifications = '''
        {
            "action": "modify",
            "modifications": [
                {
                    "type": "update",
                    "target": "h1",
                    "attribute": "textContent",
                    "content": "新标题",
                    "description": "更新标题文本"
                }
            ]
        }
        '''
        
        result = apply_html_diff(html, modifications)
        assert "新标题" in result
        assert "旧标题" not in result
    
    def test_invalid_json(self):
        """测试无效JSON处理"""
        html = '<div><h1>标题</h1></div>'
        modifications = '无效的JSON'
        
        result = apply_html_diff(html, modifications)
        # 应该返回原始HTML
        assert result == html
    
    def test_extract_modifications_from_code_snippet(self):
        """测试从code_snippet中提取修改指令"""
        response = '''
        这是一些文本。
        
        <code_snippet>
        {
            "action": "modify",
            "modifications": [
                {
                    "type": "replace",
                    "target": "h1",
                    "content": "<h1>新标题</h1>"
                }
            ]
        }
        </code_snippet>
        
        更多文本。
        '''
        
        result = extract_modifications_from_response(response)
        assert result is not None
        assert "action" in result
        assert "modify" in result
    
    def test_extract_modifications_from_json_block(self):
        """测试从JSON代码块中提取修改指令"""
        response = '''
        这是一些文本。
        
        ```json
        {
            "action": "modify",
            "modifications": [
                {
                    "type": "update",
                    "target": "h1",
                    "content": "新标题"
                }
            ]
        }
        ```
        
        更多文本。
        '''
        
        result = extract_modifications_from_response(response)
        assert result is not None
        assert "action" in result
        assert "modify" in result
    
    def test_no_modifications_found(self):
        """测试没有找到修改指令的情况"""
        response = '''
        这是一些普通的文本响应，没有任何修改指令。
        '''
        
        result = extract_modifications_from_response(response)
        assert result is None


class TestPromptTemplateLoading:
    """提示词模板加载测试"""
    
    def test_load_existing_template(self):
        """测试加载存在的模板"""
        from app.services.llm_service import load_prompt_template
        
        # 测试加载生成报告模板
        template = load_prompt_template("generate_report")
        assert template != ""
        assert "identity" in template.lower() or "报告撰写" in template
    
    def test_load_modify_template(self):
        """测试加载修改模板"""
        from app.services.llm_service import load_prompt_template
        
        # 测试加载修改报告模板
        template = load_prompt_template("modify_report")
        assert template != ""
        assert "修改" in template or "modify" in template.lower()
    
    def test_load_nonexistent_template(self):
        """测试加载不存在的模板"""
        from app.services.llm_service import load_prompt_template
        
        # 测试加载不存在的模板
        template = load_prompt_template("nonexistent_template")
        assert template == ""


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
