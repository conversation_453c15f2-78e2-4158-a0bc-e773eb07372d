#!/usr/bin/env python3
"""
测试URL编码解码的正确性
"""
import urllib.parse


def test_url_encoding_decoding():
    """测试URL编码和解码"""
    print("=== URL编码解码测试 ===")
    
    # 测试用例
    test_cases = [
        "任务 report_generation_test",
        "task_1234567890_abc123",
        "测试任务_中文字符",
        "任务",
        "Task with spaces",
        "特殊字符!@#$%^&*()",
        "emoji_test_😀🎉",
    ]
    
    for original in test_cases:
        print(f"\n原始字符串: {original}")
        
        # 前端编码（JavaScript encodeURIComponent等效）
        encoded = urllib.parse.quote(original, safe='')
        print(f"编码后: {encoded}")
        
        # 后端解码
        decoded = urllib.parse.unquote(encoded)
        print(f"解码后: {decoded}")
        
        # 验证一致性
        if original == decoded:
            print("✅ 编码解码一致")
        else:
            print("❌ 编码解码不一致")
            print(f"  原始: {repr(original)}")
            print(f"  解码: {repr(decoded)}")


def test_specific_case():
    """测试特定的问题案例"""
    print("\n=== 特定问题案例测试 ===")
    
    # 你提到的具体案例
    original_task_id = "任务 report_generation_test"
    print(f"原始任务ID: {original_task_id}")
    
    # 模拟前端编码
    encoded_url = urllib.parse.quote(original_task_id, safe='')
    print(f"前端编码后的URL: /api/tasks/{encoded_url}")
    
    # 模拟后端接收到的编码字符串
    received_encoded = "%E4%BB%BB%E5%8A%A1%20report_generation_test"
    print(f"后端接收到的编码: {received_encoded}")
    
    # 后端解码
    decoded_by_backend = urllib.parse.unquote(received_encoded)
    print(f"后端解码结果: {decoded_by_backend}")
    
    # 验证
    if original_task_id == decoded_by_backend:
        print("✅ 特定案例编码解码正确")
    else:
        print("❌ 特定案例编码解码有问题")
        print(f"  期望: {repr(original_task_id)}")
        print(f"  实际: {repr(decoded_by_backend)}")


def test_fastapi_behavior():
    """测试FastAPI的URL参数处理行为"""
    print("\n=== FastAPI URL参数处理测试 ===")
    
    # FastAPI会自动解码URL参数，但我们需要确保兼容性
    test_encoded_params = [
        "%E4%BB%BB%E5%8A%A1%20report_generation_test",  # "任务 report_generation_test"
        "task_1234567890_abc123",  # 普通ASCII
        "%E6%B5%8B%E8%AF%95",  # "测试"
    ]
    
    for encoded_param in test_encoded_params:
        print(f"\n编码参数: {encoded_param}")
        
        # 模拟FastAPI自动解码
        auto_decoded = urllib.parse.unquote(encoded_param)
        print(f"FastAPI自动解码: {auto_decoded}")
        
        # 我们的手动解码（防御性编程）
        manual_decoded = urllib.parse.unquote(encoded_param)
        print(f"手动解码: {manual_decoded}")
        
        if auto_decoded == manual_decoded:
            print("✅ 自动解码和手动解码一致")
        else:
            print("❌ 解码结果不一致")


if __name__ == "__main__":
    test_url_encoding_decoding()
    test_specific_case()
    test_fastapi_behavior()
    
    print("\n=== 总结 ===")
    print("1. 前端应使用 encodeURIComponent() 编码包含中文的任务ID")
    print("2. 后端应使用 urllib.parse.unquote() 解码URL参数")
    print("3. FastAPI会自动解码URL参数，但手动解码可以确保兼容性")
    print("4. 确保前后端使用相同的编码标准（UTF-8）")
