"""
集成测试：验证提示词系统和HTML修改逻辑
"""
import sys
import os

# 添加backend目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'backend'))

from app.services.llm_service import load_prompt_template
from app.services.html_diff_service import apply_html_diff, extract_modifications_from_response


def test_prompt_template_system():
    """测试提示词模板系统"""
    print("=== 测试提示词模板系统 ===")
    
    # 测试生成报告模板
    generate_template = load_prompt_template("generate_report")
    print(f"生成报告模板长度: {len(generate_template)}")
    print(f"生成报告模板预览: {generate_template[:200]}...")
    
    assert generate_template != "", "生成报告模板不应为空"
    assert "identity" in generate_template.lower() or "报告撰写" in generate_template, "模板应包含身份描述"
    
    # 测试修改报告模板
    modify_template = load_prompt_template("modify_report")
    print(f"修改报告模板长度: {len(modify_template)}")
    print(f"修改报告模板预览: {modify_template[:200]}...")
    
    assert modify_template != "", "修改报告模板不应为空"
    assert "修改" in modify_template or "modify" in modify_template.lower(), "模板应包含修改相关内容"
    
    print("✅ 提示词模板系统测试通过")


def test_html_diff_workflow():
    """测试HTML差异应用工作流程"""
    print("\n=== 测试HTML差异应用工作流程 ===")
    
    # 模拟原始HTML内容
    original_html = '''
    <!DOCTYPE html>
    <html>
    <head>
        <title>测试报告</title>
    </head>
    <body>
        <h1>原始标题</h1>
        <p>这是原始内容。</p>
        <div class="section">
            <h2>第一章</h2>
            <p>第一章内容</p>
        </div>
    </body>
    </html>
    '''
    
    # 模拟LLM响应（包含修改指令）
    llm_response = '''
    根据您的要求，我将修改报告标题和添加新的章节。
    
    <code_snippet>
    {
        "action": "modify",
        "modifications": [
            {
                "type": "update",
                "target": "h1",
                "attribute": "textContent",
                "content": "更新后的标题",
                "description": "更新主标题"
            },
            {
                "type": "insert",
                "target": ".section",
                "position": "after",
                "content": "<div class='section'><h2>第二章</h2><p>第二章内容</p></div>",
                "description": "添加新章节"
            }
        ]
    }
    </code_snippet>
    
    修改已完成。
    '''
    
    # 1. 提取修改指令
    print("1. 提取修改指令...")
    modifications_json = extract_modifications_from_response(llm_response)
    print(f"提取的修改指令: {modifications_json}")
    
    assert modifications_json is not None, "应该能够提取到修改指令"
    assert "action" in modifications_json, "修改指令应包含action字段"
    
    # 2. 应用修改
    print("2. 应用修改...")
    modified_html = apply_html_diff(original_html, modifications_json)
    print(f"修改后HTML长度: {len(modified_html)}")
    
    # 3. 验证修改结果
    print("3. 验证修改结果...")
    assert "更新后的标题" in modified_html, "标题应该被更新"
    assert "第二章" in modified_html, "应该添加了新章节"
    assert "原始标题" not in modified_html, "原始标题应该被替换"
    
    print("✅ HTML差异应用工作流程测试通过")


def test_error_handling():
    """测试错误处理"""
    print("\n=== 测试错误处理 ===")
    
    original_html = '<div><h1>测试</h1></div>'
    
    # 测试无效JSON
    print("1. 测试无效JSON处理...")
    invalid_json = "这不是有效的JSON"
    result = apply_html_diff(original_html, invalid_json)
    assert result == original_html, "无效JSON应该返回原始HTML"
    
    # 测试空的修改指令
    print("2. 测试空的修改指令...")
    empty_modifications = '{"action": "modify", "modifications": []}'
    result = apply_html_diff(original_html, empty_modifications)
    assert "测试" in result, "空修改指令应该保持原始内容"
    
    # 测试不存在的目标元素
    print("3. 测试不存在的目标元素...")
    invalid_target = '''
    {
        "action": "modify",
        "modifications": [
            {
                "type": "update",
                "target": "nonexistent",
                "content": "新内容"
            }
        ]
    }
    '''
    result = apply_html_diff(original_html, invalid_target)
    assert "测试" in result, "不存在的目标应该保持原始内容"
    
    print("✅ 错误处理测试通过")


def test_mode_detection():
    """测试模式检测逻辑"""
    print("\n=== 测试模式检测逻辑 ===")
    
    # 模拟生成模式（无context_html）
    print("1. 测试生成模式...")
    user_message = "请生成一个关于气候变化的报告"
    context_html = ""
    
    # 根据context_html判断模式
    if context_html and context_html.strip():
        mode = "modify"
    else:
        mode = "generate"
    
    assert mode == "generate", "无context_html应该是生成模式"
    print(f"检测到模式: {mode}")
    
    # 模拟修改模式（有context_html）
    print("2. 测试修改模式...")
    user_message = "请修改报告标题"
    context_html = "<html><body><h1>原标题</h1></body></html>"
    
    if context_html and context_html.strip():
        mode = "modify"
    else:
        mode = "generate"
    
    assert mode == "modify", "有context_html应该是修改模式"
    print(f"检测到模式: {mode}")
    
    print("✅ 模式检测逻辑测试通过")


if __name__ == "__main__":
    """运行所有集成测试"""
    print("开始运行集成测试...\n")
    
    try:
        test_prompt_template_system()
        test_html_diff_workflow()
        test_error_handling()
        test_mode_detection()
        
        print("\n🎉 所有集成测试通过！")
        print("\n新功能验证完成：")
        print("✅ 提示词文件系统正常工作")
        print("✅ HTML差异应用逻辑正常工作")
        print("✅ 模式检测逻辑正常工作")
        print("✅ 错误处理机制正常工作")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
