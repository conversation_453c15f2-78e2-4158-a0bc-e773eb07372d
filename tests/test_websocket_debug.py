"""
WebSocket调试测试
用于验证前后端消息传输
"""
import asyncio
import json
import websockets
import sys
import os

# 添加backend目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'backend'))


async def test_websocket_message_format():
    """测试WebSocket消息格式"""
    print("=== WebSocket消息格式测试 ===")
    
    # 模拟前端发送的消息格式
    test_messages = [
        {
            "type": "user_message",
            "content": "请生成一个关于气候变化的报告",
            "context_html": ""
        },
        {
            "type": "user_message", 
            "content": "请修改报告标题",
            "context_html": "<html><body><h1>原标题</h1><p>内容</p></body></html>"
        },
        {
            "type": "user_message",
            "content": "添加新的章节",
            "context_html": "<div><h1>现有标题</h1><section>现有章节</section></div>"
        }
    ]
    
    for i, message in enumerate(test_messages, 1):
        print(f"\n--- 测试消息 {i} ---")
        print(f"消息类型: {message['type']}")
        print(f"内容长度: {len(message['content'])}")
        print(f"上下文HTML长度: {len(message['context_html'])}")
        print(f"有上下文: {bool(message['context_html'].strip())}")
        
        # 验证JSON序列化
        try:
            json_str = json.dumps(message)
            parsed = json.loads(json_str)
            print(f"JSON序列化: ✅ 成功")
            print(f"解析后内容: {parsed.get('content', '')[:50]}...")
            print(f"解析后上下文长度: {len(parsed.get('context_html', ''))}")
        except Exception as e:
            print(f"JSON序列化: ❌ 失败 - {e}")


async def test_websocket_connection():
    """测试WebSocket连接（如果后端运行中）"""
    print("\n=== WebSocket连接测试 ===")
    
    # 测试连接到本地WebSocket服务器
    ws_url = "ws://localhost:8000/ws/chat/test_task_123"
    
    try:
        print(f"尝试连接到: {ws_url}")
        
        async with websockets.connect(ws_url) as websocket:
            print("✅ WebSocket连接成功")
            
            # 发送测试消息
            test_message = {
                "type": "user_message",
                "content": "这是一个测试消息",
                "context_html": ""
            }
            
            print(f"发送消息: {test_message}")
            await websocket.send(json.dumps(test_message))
            
            # 等待响应
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                print(f"收到响应: {response[:100]}...")
            except asyncio.TimeoutError:
                print("⚠️  等待响应超时")
                
    except ConnectionRefusedError:
        print("❌ 连接被拒绝 - 后端服务器可能未运行")
    except Exception as e:
        print(f"❌ 连接失败: {e}")


def test_message_parsing():
    """测试消息解析逻辑"""
    print("\n=== 消息解析测试 ===")
    
    # 模拟后端接收到的原始数据
    raw_messages = [
        '{"type": "user_message", "content": "测试消息", "context_html": ""}',
        '{"type": "user_message", "content": "修改请求", "context_html": "<div>HTML内容</div>"}',
        '{"type": "invalid_type", "content": "无效类型"}',
        '{"content": "缺少type字段", "context_html": ""}',
        'invalid json',
        ''
    ]
    
    for i, raw_data in enumerate(raw_messages, 1):
        print(f"\n--- 解析测试 {i} ---")
        print(f"原始数据: {raw_data}")
        
        try:
            # 模拟后端解析逻辑
            message_data = json.loads(raw_data)
            message_type = message_data.get("type", "")
            user_message = message_data.get("content", "")
            context_html = message_data.get("context_html", "")
            
            print(f"解析结果:")
            print(f"  - 消息类型: '{message_type}'")
            print(f"  - 用户消息: '{user_message}'")
            print(f"  - 上下文HTML长度: {len(context_html)}")
            print(f"  - 有上下文: {bool(context_html.strip())}")
            
            # 验证消息类型
            if message_type != "user_message":
                print(f"  ⚠️  意外的消息类型: {message_type}")
            
            # 验证消息内容
            if not user_message.strip():
                print(f"  ⚠️  空的用户消息")
            
            print(f"  ✅ 解析成功")
            
        except json.JSONDecodeError as e:
            print(f"  ❌ JSON解析失败: {e}")
        except Exception as e:
            print(f"  ❌ 其他错误: {e}")


def test_context_detection():
    """测试上下文检测逻辑"""
    print("\n=== 上下文检测测试 ===")
    
    test_cases = [
        ("", "无上下文"),
        ("   ", "空白上下文"),
        ("<div>简单HTML</div>", "简单HTML"),
        ("<html><body><h1>完整页面</h1></body></html>", "完整HTML页面"),
        ("普通文本", "普通文本（非HTML）")
    ]
    
    for context_html, description in test_cases:
        print(f"\n--- {description} ---")
        print(f"上下文内容: '{context_html}'")
        print(f"长度: {len(context_html)}")
        print(f"去空格后长度: {len(context_html.strip())}")
        
        # 模拟模式检测逻辑
        if context_html and context_html.strip():
            mode = "modify"
            print(f"检测模式: 修改模式")
        else:
            mode = "generate"
            print(f"检测模式: 生成模式")


if __name__ == "__main__":
    """运行所有调试测试"""
    print("开始WebSocket调试测试...\n")
    
    try:
        # 运行同步测试
        test_message_parsing()
        test_context_detection()
        
        # 运行异步测试
        asyncio.run(test_websocket_message_format())
        asyncio.run(test_websocket_connection())
        
        print("\n🎉 WebSocket调试测试完成！")
        print("\n调试建议：")
        print("1. 检查前端是否正确设置selectedContext")
        print("2. 检查WebSocket连接状态")
        print("3. 查看浏览器开发者工具的Network标签")
        print("4. 检查后端日志输出")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
