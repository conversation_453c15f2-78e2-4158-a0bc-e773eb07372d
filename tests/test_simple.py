"""
简单测试：验证核心功能而不依赖配置
"""
import sys
import os
import json

# 添加backend目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'backend'))

# 直接导入HTML diff服务（不依赖配置）
try:
    from bs4 import BeautifulSoup
    BEAUTIFULSOUP_AVAILABLE = True
except ImportError:
    BEAUTIFULSOUP_AVAILABLE = False
    print("⚠️  BeautifulSoup4 未安装，跳过HTML diff测试")


def test_prompt_files_exist():
    """测试提示词文件是否存在"""
    print("=== 测试提示词文件 ===")
    
    backend_dir = os.path.join(os.path.dirname(__file__), '..', 'backend')
    prompts_dir = os.path.join(backend_dir, 'app', 'prompts')
    
    # 检查prompts目录
    assert os.path.exists(prompts_dir), f"prompts目录不存在: {prompts_dir}"
    print(f"✅ prompts目录存在: {prompts_dir}")
    
    # 检查生成报告模板
    generate_file = os.path.join(prompts_dir, 'generate_report.txt')
    assert os.path.exists(generate_file), f"generate_report.txt不存在: {generate_file}"
    print(f"✅ generate_report.txt存在")
    
    # 检查修改报告模板
    modify_file = os.path.join(prompts_dir, 'modify_report.txt')
    assert os.path.exists(modify_file), f"modify_report.txt不存在: {modify_file}"
    print(f"✅ modify_report.txt存在")
    
    # 检查文件内容
    with open(generate_file, 'r', encoding='utf-8') as f:
        generate_content = f.read()
    assert len(generate_content) > 100, "generate_report.txt内容太短"
    assert "报告" in generate_content or "report" in generate_content.lower(), "generate_report.txt应包含报告相关内容"
    print(f"✅ generate_report.txt内容有效 (长度: {len(generate_content)})")
    
    with open(modify_file, 'r', encoding='utf-8') as f:
        modify_content = f.read()
    assert len(modify_content) > 100, "modify_report.txt内容太短"
    assert "修改" in modify_content or "modify" in modify_content.lower(), "modify_report.txt应包含修改相关内容"
    print(f"✅ modify_report.txt内容有效 (长度: {len(modify_content)})")


def test_html_diff_logic():
    """测试HTML diff逻辑（如果BeautifulSoup可用）"""
    if not BEAUTIFULSOUP_AVAILABLE:
        print("⚠️  跳过HTML diff测试（BeautifulSoup4未安装）")
        return
    
    print("\n=== 测试HTML diff逻辑 ===")
    
    # 简单的HTML diff测试
    from app.services.html_diff_service import HTMLDiffApplier, extract_modifications_from_response
    
    # 测试1: 提取修改指令
    print("1. 测试提取修改指令...")
    llm_response = '''
    这是一些文本。
    
    <code_snippet>
    {
        "action": "modify",
        "modifications": [
            {
                "type": "replace",
                "target": "h1",
                "content": "<h1>新标题</h1>",
                "description": "替换标题"
            }
        ]
    }
    </code_snippet>
    
    更多文本。
    '''
    
    modifications = extract_modifications_from_response(llm_response)
    assert modifications is not None, "应该能提取到修改指令"
    assert "action" in modifications, "修改指令应包含action字段"
    print("✅ 修改指令提取成功")
    
    # 测试2: 应用简单修改
    print("2. 测试应用简单修改...")
    html = '<div><h1>原标题</h1><p>内容</p></div>'
    applier = HTMLDiffApplier(html)
    
    result = applier.apply_modifications(modifications)
    assert "新标题" in result, "应该包含新标题"
    print("✅ HTML修改应用成功")
    
    # 测试3: 错误处理
    print("3. 测试错误处理...")
    invalid_json = "无效的JSON"
    result = applier.apply_modifications(invalid_json)
    assert result == html, "无效JSON应该返回原始HTML"
    print("✅ 错误处理正常")


def test_service_files_exist():
    """测试服务文件是否存在"""
    print("\n=== 测试服务文件 ===")
    
    backend_dir = os.path.join(os.path.dirname(__file__), '..', 'backend')
    services_dir = os.path.join(backend_dir, 'app', 'services')
    
    # 检查LLM服务文件
    llm_service_file = os.path.join(services_dir, 'llm_service.py')
    assert os.path.exists(llm_service_file), f"llm_service.py不存在: {llm_service_file}"
    print(f"✅ llm_service.py存在")
    
    # 检查HTML diff服务文件
    html_diff_file = os.path.join(services_dir, 'html_diff_service.py')
    assert os.path.exists(html_diff_file), f"html_diff_service.py不存在: {html_diff_file}"
    print(f"✅ html_diff_service.py存在")
    
    # 检查LLM服务文件内容
    with open(llm_service_file, 'r', encoding='utf-8') as f:
        llm_content = f.read()
    assert "load_prompt_template" in llm_content, "llm_service.py应包含load_prompt_template函数"
    assert "generate_llm_response" in llm_content, "llm_service.py应包含generate_llm_response函数"
    print(f"✅ llm_service.py内容有效")
    
    # 检查HTML diff服务文件内容
    with open(html_diff_file, 'r', encoding='utf-8') as f:
        diff_content = f.read()
    assert "HTMLDiffApplier" in diff_content, "html_diff_service.py应包含HTMLDiffApplier类"
    assert "apply_html_diff" in diff_content, "html_diff_service.py应包含apply_html_diff函数"
    print(f"✅ html_diff_service.py内容有效")


def test_websocket_integration():
    """测试WebSocket集成"""
    print("\n=== 测试WebSocket集成 ===")
    
    backend_dir = os.path.join(os.path.dirname(__file__), '..', 'backend')
    websocket_file = os.path.join(backend_dir, 'app', 'api', 'websocket.py')
    
    assert os.path.exists(websocket_file), f"websocket.py不存在: {websocket_file}"
    
    with open(websocket_file, 'r', encoding='utf-8') as f:
        websocket_content = f.read()
    
    # 检查是否导入了新的服务
    assert "html_diff_service" in websocket_content, "websocket.py应导入html_diff_service"
    assert "apply_html_diff" in websocket_content, "websocket.py应导入apply_html_diff函数"
    assert "extract_modifications_from_response" in websocket_content, "websocket.py应导入extract_modifications_from_response函数"
    
    # 检查是否有修改模式的处理逻辑
    assert "context_html" in websocket_content, "websocket.py应包含context_html处理"
    assert "modifications_json" in websocket_content, "websocket.py应包含modifications_json处理"
    
    print(f"✅ WebSocket集成检查通过")


def test_requirements():
    """测试依赖文件"""
    print("\n=== 测试依赖文件 ===")
    
    backend_dir = os.path.join(os.path.dirname(__file__), '..', 'backend')
    requirements_file = os.path.join(backend_dir, 'requirements.txt')
    
    assert os.path.exists(requirements_file), f"requirements.txt不存在: {requirements_file}"
    
    with open(requirements_file, 'r', encoding='utf-8') as f:
        requirements_content = f.read()
    
    assert "beautifulsoup4" in requirements_content, "requirements.txt应包含beautifulsoup4依赖"
    print(f"✅ requirements.txt包含必要依赖")


if __name__ == "__main__":
    """运行所有简单测试"""
    print("开始运行简单测试...\n")
    
    try:
        test_prompt_files_exist()
        test_service_files_exist()
        test_websocket_integration()
        test_requirements()
        test_html_diff_logic()
        
        print("\n🎉 所有简单测试通过！")
        print("\n功能实现验证：")
        print("✅ 提示词文件系统已实现")
        print("✅ HTML差异应用服务已实现")
        print("✅ WebSocket集成已完成")
        print("✅ 依赖管理已更新")
        
        if BEAUTIFULSOUP_AVAILABLE:
            print("✅ HTML diff功能测试通过")
        else:
            print("⚠️  HTML diff功能需要安装beautifulsoup4")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
