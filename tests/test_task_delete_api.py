"""
测试任务删除API功能
"""
import pytest
import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "backend"))

from app.services.task_service import create_task, delete_task, get_task_by_id
from app.core.database import init_database


class TestTaskDeleteAPI:
    """测试任务删除API功能"""
    
    @pytest.fixture(autouse=True)
    async def setup_database(self):
        """设置测试数据库"""
        await init_database()
        yield
        # 测试后清理可以在这里添加
    
    @pytest.mark.asyncio
    async def test_delete_single_task(self):
        """测试删除单个任务"""
        # 创建测试任务
        task = await create_task("测试删除任务")
        task_id = task.id
        
        # 验证任务存在
        existing_task = await get_task_by_id(task_id)
        assert existing_task is not None
        assert existing_task.title == "测试删除任务"
        
        # 删除任务
        success = await delete_task(task_id)
        assert success is True
        
        # 验证任务已被删除
        deleted_task = await get_task_by_id(task_id)
        assert deleted_task is None
    
    @pytest.mark.asyncio
    async def test_delete_nonexistent_task(self):
        """测试删除不存在的任务"""
        # 尝试删除不存在的任务
        success = await delete_task("nonexistent_task_id")
        assert success is False
    

if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
