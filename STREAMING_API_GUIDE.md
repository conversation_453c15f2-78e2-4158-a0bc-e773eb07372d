# 流式接口使用指南

## 概述

我们已经成功将 `send_message` 接口从普通的 POST 接口升级为流式接口，这样前端就能实时看到任务的执行情况。

## 后端变更

### 1. 新的流式响应接口

**路径**: `POST /api/tasks/{task_id}/messages`

**响应格式**: Server-Sent Events (SSE) 流式响应

**响应数据结构**:
```typescript
interface StreamChunk {
  type: 'start' | 'status' | 'assistant_chunk' | 'code_processed' | 'warning' | 'completed' | 'error' | 'end'
  content: string
  progress?: number
  has_code_snippet?: boolean
  timestamp: number
}
```

### 2. 流式响应类型说明

- `start`: 开始处理消息
- `status`: 状态更新（如"正在保存用户消息..."）
- `assistant_chunk`: AI 响应的文本块
- `code_processed`: 代码片段处理完成
- `warning`: 处理过程中的警告
- `completed`: 消息处理完成
- `error`: 处理过程中的错误
- `end`: 流式响应结束信号

### 3. 实现细节

- 使用 FastAPI 的 `StreamingResponse` 实现流式响应
- 每个响应块都是 JSON 格式，以 `data: ` 前缀发送
- 包含进度信息，前端可以显示处理进度条
- 错误处理：如果处理过程中出现错误，会发送错误类型的响应块

## 前端变更

### 1. 新的流式消息服务

**文件**: `frontend/src/services/api/messageService.ts`

新增了 `sendMessageStream` 方法：
```typescript
sendMessageStream: async (
  taskId: string, 
  content: string, 
  contextHtml?: string,
  onChunk?: (chunk: StreamChunk) => void
): Promise<void>
```

### 2. HTTP 聊天服务更新

**文件**: `frontend/src/services/http/chatService.ts`

- 添加了流式响应块的监听器
- `sendMessage` 方法现在使用流式接口
- 根据流式响应类型自动更新连接状态

### 3. 聊天状态管理

**文件**: `frontend/src/stores/chatStore.ts`

- 添加了 `currentStreamChunk` 状态
- 监听流式响应块并更新状态
- 根据响应类型更新进度和错误信息

### 4. 新的流式指示器组件

**文件**: `frontend/src/components/chat/StreamingIndicator/index.tsx`

- 显示当前流式响应的状态
- 根据响应类型显示不同的图标和颜色
- 支持进度条显示

### 5. 消息列表更新

**文件**: `frontend/src/components/chat/MessageList/index.tsx`

- 集成了流式指示器组件
- 在有流式响应时显示实时状态

## 使用方法

### 1. 发送消息

前端发送消息时，会自动使用流式接口：

```typescript
// 在聊天界面中发送消息
const handleSendMessage = (content: string, contextHtml?: string) => {
  sendMessage(content, contextHtml) // 自动使用流式接口
}
```

### 2. 监听流式响应

可以通过 chatStore 监听流式响应：

```typescript
const { currentStreamChunk } = useChatStore()

// 根据响应类型处理
if (currentStreamChunk) {
  switch (currentStreamChunk.type) {
    case 'start':
      // 开始处理
      break
    case 'status':
      // 状态更新
      break
    case 'assistant_chunk':
      // AI 响应块
      break
    case 'completed':
      // 处理完成
      break
    case 'error':
      // 处理错误
      break
  }
}
```

## 测试方法

### 1. 启动服务

```bash
# 启动后端
cd backend
python -m uvicorn main:app --reload --host 0.0.0.0 --port 8000

# 启动前端
cd frontend
npm run dev
```

### 2. 测试流式接口

1. 打开浏览器访问 `http://localhost:3002`
2. 选择或创建一个任务
3. 发送消息，观察流式响应指示器
4. 可以看到实时的处理状态和进度

### 3. 使用 curl 测试后端

```bash
curl -X POST "http://localhost:8000/api/tasks/test_task/messages" \
  -H "Content-Type: application/json" \
  -d '{"content": "请生成一个简单的HTML报告", "context_html": ""}' \
  --no-buffer
```

## 优势

1. **实时反馈**: 用户可以实时看到任务执行进度
2. **更好的用户体验**: 不再需要等待整个响应完成
3. **错误处理**: 可以实时显示处理过程中的错误和警告
4. **进度显示**: 支持进度条显示处理进度
5. **状态透明**: 用户可以清楚地知道当前处理到哪个阶段

## 兼容性

- 保持了原有 API 的兼容性
- 前端的 `sendMessage` 方法仍然可以正常使用
- 旧的轮询机制作为备用方案保留

## 注意事项

1. 流式响应需要保持连接，确保网络稳定
2. 如果连接中断，前端会自动回退到轮询模式
3. 流式响应的数据量可能较大，注意网络带宽
4. 确保后端的 LLM 服务支持流式响应
