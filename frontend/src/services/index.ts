// API服务导出
export { APIClient, apiClient } from './api/client'
export { TaskService, taskService } from './api/taskService'
export { ConfigService, configService } from './api/configService'

// HTTP聊天服务导出
export { HTTPChatService, httpChatService } from './http/chatService'
export type { ConnectionStatus } from './http/chatService'

// 服务类型导出
export type {
  APIConfig,
  RequestConfig,
  RequestInterceptor,
  ResponseInterceptor,
  ErrorInterceptor
} from './api/client'
