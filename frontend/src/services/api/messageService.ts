import { apiClient } from './client'
import type { Message, StreamChunk } from '../../types/api'

export const messageService = {
  // 获取任务的消息历史
  getTaskMessages: async (taskId: string): Promise<Message[]> => {
    try {
      console.log(`Fetching messages for task: ${taskId}`)
      const response: any = await apiClient.get(`/api/tasks/${taskId}`)
      console.log('Raw API response:', response)

      // 直接访问response.data.messages
      if (response?.data?.messages && Array.isArray(response.data.messages)) {
        const messages = response.data.messages as Message[]
        console.log(`Successfully loaded ${messages.length} messages:`, messages)
        return messages
      }

      console.log('No messages found in response.data.messages, returning empty array')
      return []
    } catch (error) {
      console.error('Failed to fetch task messages:', error)
      // 返回空数组作为fallback
      return []
    }
  },

  // 发送消息 (流式)
  sendMessageStream: async (
    taskId: string,
    content: string,
    contextHtml?: string,
    onChunk?: (chunk: StreamChunk) => void
  ): Promise<void> => {
    const encodedTaskId = encodeURIComponent(taskId)
    const response = await fetch(`/api/tasks/${encodedTaskId}/messages`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        content,
        context_html: contextHtml
      })
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const reader = response.body?.getReader()
    if (!reader) {
      throw new Error('No response body reader available')
    }

    const decoder = new TextDecoder()
    let buffer = ''

    try {
      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        buffer += decoder.decode(value, { stream: true })

        // 处理完整的数据行
        const lines = buffer.split('\n')
        buffer = lines.pop() || '' // 保留最后一个不完整的行

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6))
              if (onChunk) {
                onChunk(data)
              }
            } catch (e) {
              console.warn('Failed to parse stream chunk:', line)
            }
          }
        }
      }
    } finally {
      reader.releaseLock()
    }
  },

  // 发送消息 (兼容旧版本)
  sendMessage: async (taskId: string, content: string, contextHtml?: string): Promise<Message> => {
    // 这个方法现在使用流式接口，但返回一个Promise来保持兼容性
    return new Promise((resolve, reject) => {
      let assistantMessage: Message | null = null

      messageService.sendMessageStream(taskId, content, contextHtml, (chunk) => {
        if (chunk.type === 'completed' && assistantMessage) {
          resolve(assistantMessage)
        } else if (chunk.type === 'error') {
          reject(new Error(chunk.content))
        }
        // 这里可以根据需要处理其他类型的chunk
      }).catch(reject)
    })
  },

  // 删除消息
  deleteMessage: async (taskId: string, messageId: string): Promise<void> => {
    const encodedTaskId = encodeURIComponent(taskId)
    const encodedMessageId = encodeURIComponent(messageId)
    await apiClient.delete(`/api/tasks/${encodedTaskId}/messages/${encodedMessageId}`)
  },

  // 重新生成消息
  regenerateMessage: async (taskId: string, messageId: string): Promise<Message> => {
    const encodedTaskId = encodeURIComponent(taskId)
    const encodedMessageId = encodeURIComponent(messageId)
    const response = await apiClient.post(`/api/tasks/${encodedTaskId}/messages/${encodedMessageId}/regenerate`) as { data: Message }
    return response.data
  }
}
