import { APIError, NetworkError, TimeoutError } from '../../types/api'

// API配置接口
export interface APIConfig {
  baseURL: string
  timeout: number
  retryAttempts: number
  retryDelay: number
}

// 请求配置接口
export interface RequestConfig {
  url: string
  method: 'GET' | 'POST' | 'PUT' | 'DELETE'
  headers?: Record<string, string>
  data?: any
  params?: Record<string, any>
}

// 请求和响应拦截器类型
export type RequestInterceptor = (config: RequestConfig) => RequestConfig
export type ResponseInterceptor = (response: any) => any
export type ErrorInterceptor = (error: any) => Promise<never>

// 基础API客户端类
export class APIClient {
  private config: APIConfig
  private requestInterceptors: RequestInterceptor[] = []
  private responseInterceptors: ResponseInterceptor[] = []
  private errorInterceptors: ErrorInterceptor[] = []

  constructor(config: Partial<APIConfig> = {}) {
    this.config = {
      baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080',
      timeout: 10000,
      retryAttempts: 3,
      retryDelay: 1000,
      ...config
    }
  }

  // 添加请求拦截器
  addRequestInterceptor(interceptor: RequestInterceptor): void {
    this.requestInterceptors.push(interceptor)
  }

  // 添加响应拦截器
  addResponseInterceptor(interceptor: ResponseInterceptor): void {
    this.responseInterceptors.push(interceptor)
  }

  // 添加错误拦截器
  addErrorInterceptor(interceptor: ErrorInterceptor): void {
    this.errorInterceptors.push(interceptor)
  }

  // HTTP GET请求
  async get<T>(endpoint: string, params?: any): Promise<T> {
    return this.request<T>({
      url: endpoint,
      method: 'GET',
      params
    })
  }

  // HTTP POST请求
  async post<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>({
      url: endpoint,
      method: 'POST',
      data
    })
  }

  // HTTP PUT请求
  async put<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>({
      url: endpoint,
      method: 'PUT',
      data
    })
  }

  // HTTP DELETE请求
  async delete<T>(endpoint: string): Promise<T> {
    return this.request<T>({
      url: endpoint,
      method: 'DELETE'
    })
  }

  // 核心请求方法
  private async request<T>(config: RequestConfig): Promise<T> {
    return this.retry(() => this.executeRequest<T>(config), this.config.retryAttempts)
  }

  // 执行请求
  private async executeRequest<T>(config: RequestConfig): Promise<T> {
    // 应用请求拦截器
    let processedConfig = config
    for (const interceptor of this.requestInterceptors) {
      processedConfig = interceptor(processedConfig)
    }

    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), this.config.timeout)

    try {
      const url = this.buildURL(processedConfig.url, processedConfig.params)
      const response = await fetch(url, {
        method: processedConfig.method,
        headers: {
          'Content-Type': 'application/json',
          ...processedConfig.headers
        },
        body: processedConfig.data ? JSON.stringify(processedConfig.data) : undefined,
        signal: controller.signal
      })

      clearTimeout(timeoutId)

      if (!response.ok) {
        throw new APIError(response.status, response.statusText)
      }

      let data = await response.json()

      // 应用响应拦截器
      for (const interceptor of this.responseInterceptors) {
        data = interceptor(data)
      }

      return data
    } catch (error) {
      clearTimeout(timeoutId)
      
      // 应用错误拦截器
      for (const interceptor of this.errorInterceptors) {
        await interceptor(error)
      }

      throw this.normalizeError(error)
    }
  }

  // 重试机制
  private async retry<T>(fn: () => Promise<T>, attempts: number): Promise<T> {
    try {
      return await fn()
    } catch (error) {
      if (attempts > 0 && this.shouldRetry(error)) {
        await this.delay(this.config.retryDelay)
        return this.retry(fn, attempts - 1)
      }
      throw error
    }
  }

  // 判断是否应该重试
  private shouldRetry(error: any): boolean {
    return (
      error.name === 'NetworkError' ||
      error.name === 'TimeoutError' ||
      (error instanceof APIError && error.status >= 500 && error.status < 600)
    )
  }

  // 延迟函数
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  // 构建URL
  private buildURL(endpoint: string, params?: Record<string, any>): string {
    const url = new URL(endpoint, this.config.baseURL)
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          url.searchParams.append(key, String(value))
        }
      })
    }
    
    return url.toString()
  }

  // 标准化错误
  private normalizeError(error: any): Error {
    if (error.name === 'AbortError') {
      return new TimeoutError('Request timeout')
    }
    
    if (error instanceof TypeError && error.message.includes('fetch')) {
      return new NetworkError('Network connection failed')
    }
    
    if (error instanceof APIError) {
      return error
    }
    
    return new Error(error.message || 'Unknown error occurred')
  }
}

// 创建默认API客户端实例
export const apiClient = new APIClient()

// 设置默认拦截器
apiClient.addRequestInterceptor((config) => {
  // 添加认证头（如果需要）
  const token = localStorage.getItem('auth_token')
  if (token) {
    config.headers = {
      ...config.headers,
      Authorization: `Bearer ${token}`
    }
  }

  // 添加请求ID用于追踪
  config.headers = {
    ...config.headers,
    'X-Request-ID': generateRequestId()
  }

  return config
})

apiClient.addResponseInterceptor((response) => {
  // 处理成功响应
  return response
})

apiClient.addErrorInterceptor(async (error) => {
  // 处理认证错误
  if (error instanceof APIError && error.status === 401) {
    // 清除认证信息
    localStorage.removeItem('auth_token')
    // 可以触发重新登录流程
    console.warn('Authentication failed, please login again')
  }
  
  // 记录错误
  console.error('API Error:', error)
  
  throw error
})

// 生成请求ID的工具函数
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}
