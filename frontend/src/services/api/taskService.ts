import { APIClient } from './client'
import type {
  APIResponse,
  Task,
  TaskDetail,
  TaskQueryParams
} from '../../types/api'

export class TaskService extends APIClient {
  // 获取任务列表
  async getTasks(params?: TaskQueryParams): Promise<APIResponse<Task[]>> {
    const queryParams: Record<string, any> = {}
    
    if (params?.limit) queryParams.limit = params.limit
    if (params?.offset) queryParams.offset = params.offset
    if (params?.search) queryParams.search = params.search
    if (params?.dateRange) {
      queryParams.start_date = params.dateRange[0]
      queryParams.end_date = params.dateRange[1]
    }
    
    return this.get<APIResponse<Task[]>>('/api/tasks', queryParams)
  }

  // 获取任务详情
  async getTask(taskId: string): Promise<APIResponse<TaskDetail>> {
    // 确保任务ID被正确编码
    const encodedTaskId = encodeURIComponent(taskId)
    return this.get<APIResponse<TaskDetail>>(`/api/tasks/${encodedTaskId}`)
  }

  // 创建新任务（通过WebSocket实现，这里返回临时任务对象）
  async createTask(title: string): Promise<Task> {
    // 生成临时任务ID
    const taskId = this.generateTaskId()
    
    // 返回临时任务对象，实际创建通过WebSocket完成
    return {
      id: taskId,
      title,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      message_count: 0,
      report_file_path: null,
      report_html_filename: 'report.html'
    }
  }

  // 更新任务（预留接口）
  async updateTask(taskId: string, updates: Partial<Task>): Promise<APIResponse<Task>> {
    const encodedTaskId = encodeURIComponent(taskId)
    return this.put<APIResponse<Task>>(`/api/tasks/${encodedTaskId}`, updates)
  }

  // 删除任务（预留接口）
  async deleteTask(taskId: string): Promise<APIResponse<void>> {
    const encodedTaskId = encodeURIComponent(taskId)
    return this.delete<APIResponse<void>>(`/api/tasks/${encodedTaskId}`)
  }

  // 批量删除任务（预留接口）
  async deleteMultipleTasks(taskIds: string[]): Promise<APIResponse<void>> {
    return this.post<APIResponse<void>>('/api/tasks/batch-delete', { task_ids: taskIds })
  }

  // 检查任务是否存在
  async checkTaskExists(taskId: string): Promise<boolean> {
    try {
      await this.getTask(taskId)
      return true
    } catch (error) {
      return false
    }
  }

  // 搜索任务
  async searchTasks(query: string, limit = 20): Promise<APIResponse<Task[]>> {
    return this.getTasks({ search: query, limit })
  }

  // 获取最近任务
  async getRecentTasks(limit = 10): Promise<APIResponse<Task[]>> {
    return this.getTasks({ limit })
  }

  // 生成任务ID的工具方法
  private generateTaskId(): string {
    const timestamp = Date.now()
    const random = Math.random().toString(36).substr(2, 9)
    return `task_${timestamp}_${random}`
  }




}

// 创建任务服务实例
export const taskService = new TaskService()
