import React, { useState, useEffect } from 'react'
import { useChatStore } from '../../stores/chatStore'
import StreamingMessage from '../chat/StreamingMessage'

const StreamingDemo: React.FC = () => {
  const [isRunning, setIsRunning] = useState(false)
  const { 
    streamingDisplayText, 
    isCodeSnippetMode, 
    isStreamActive,
    updateStreamingBuffer,
    completeStreaming,
    resetStreamingState
  } = useChatStore()

  // 模拟流式数据
  const simulateStreaming = async () => {
    if (isRunning) return
    
    setIsRunning(true)
    resetStreamingState()

    const chunks = [
      '您好！我将为您生成一份详细的数据分析报告。',
      '首先，让我分析您提供的数据...',
      '\n\n正在处理数据中，请稍候...',
      '\n\n根据分析结果，我发现了以下几个关键点：',
      '\n1. 数据质量良好，完整性达到95%',
      '\n2. 存在明显的季节性趋势',
      '\n3. 异常值较少，数据分布正常',
      '\n\n现在我将为您生成可视化报告：',
      '\n\n<code_snippet>',
      '\n<div class="report-container">',
      '\n  <h1>数据分析报告</h1>',
      '\n  <div class="summary">',
      '\n    <h2>执行摘要</h2>',
      '\n    <p>本报告基于提供的数据集进行深入分析...</p>',
      '\n  </div>',
      '\n  <div class="charts">',
      '\n    <h2>数据可视化</h2>',
      '\n    <div class="chart-grid">',
      '\n      <div class="chart">趋势图</div>',
      '\n      <div class="chart">分布图</div>',
      '\n    </div>',
      '\n  </div>',
      '\n  <div class="insights">',
      '\n    <h2>关键洞察</h2>',
      '\n    <ul>',
      '\n      <li>季节性模式明显</li>',
      '\n      <li>增长趋势稳定</li>',
      '\n      <li>异常值影响较小</li>',
      '\n    </ul>',
      '\n  </div>',
      '\n</div>',
      '\n</code_snippet>',
      '\n\n报告已生成完成！',
      '\n\n您可以在右侧查看完整的HTML报告。报告包含了详细的数据分析、可视化图表和关键洞察。',
      '\n\n如果您需要修改报告的任何部分，请告诉我具体的要求。'
    ]

    // 模拟流式传输
    for (let i = 0; i < chunks.length; i++) {
      await new Promise(resolve => setTimeout(resolve, 200 + Math.random() * 300))
      updateStreamingBuffer(chunks[i])
    }

    // 延时后完成流式传输
    await new Promise(resolve => setTimeout(resolve, 500))
    completeStreaming()
    setIsRunning(false)
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">
          流式消息处理演示
        </h1>
        
        <div className="mb-6">
          <h2 className="text-lg font-semibold text-gray-800 mb-2">功能说明</h2>
          <ul className="text-sm text-gray-600 space-y-1">
            <li>• 接收流式数据并维护20字符缓冲区</li>
            <li>• 检测 &lt;code_snippet&gt; 标识并显示"报告生成中"状态</li>
            <li>• 隐藏 &lt;code_snippet&gt; 标签之间的内容</li>
            <li>• 流式传输完成后延时500ms刷新报告内容</li>
          </ul>
        </div>

        <div className="mb-6">
          <button
            onClick={simulateStreaming}
            disabled={isRunning}
            className={`px-4 py-2 rounded-lg font-medium ${
              isRunning
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-blue-600 text-white hover:bg-blue-700'
            }`}
          >
            {isRunning ? '正在演示...' : '开始演示'}
          </button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 流式消息显示区域 */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-md font-semibold text-gray-800 mb-3">
              流式消息显示
            </h3>
            <div className="min-h-[300px] bg-white rounded border p-4">
              {isStreamActive ? (
                <StreamingMessage />
              ) : (
                <div className="text-gray-500 text-sm">
                  点击"开始演示"按钮查看流式消息效果
                </div>
              )}
            </div>
          </div>

          {/* 状态信息 */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-md font-semibold text-gray-800 mb-3">
              状态信息
            </h3>
            <div className="space-y-3 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">流式状态:</span>
                <span className={`font-medium ${isStreamActive ? 'text-green-600' : 'text-gray-500'}`}>
                  {isStreamActive ? '活跃' : '非活跃'}
                </span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-gray-600">代码片段模式:</span>
                <span className={`font-medium ${isCodeSnippetMode ? 'text-blue-600' : 'text-gray-500'}`}>
                  {isCodeSnippetMode ? '是' : '否'}
                </span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-gray-600">显示文本长度:</span>
                <span className="font-medium text-gray-800">
                  {streamingDisplayText.length} 字符
                </span>
              </div>

              <div className="mt-4">
                <span className="text-gray-600 block mb-2">当前显示文本预览:</span>
                <div className="bg-white border rounded p-2 text-xs text-gray-700 max-h-32 overflow-y-auto">
                  {streamingDisplayText || '(空)'}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 技术说明 */}
        <div className="mt-8 bg-blue-50 rounded-lg p-4">
          <h3 className="text-md font-semibold text-blue-800 mb-2">
            技术实现说明
          </h3>
          <div className="text-sm text-blue-700 space-y-2">
            <p>
              <strong>缓冲区机制:</strong> 始终保持20个字符的缓冲区，防止不完整内容显示给用户
            </p>
            <p>
              <strong>代码片段检测:</strong> 自动检测 &lt;code_snippet&gt; 标识，进入报告生成模式
            </p>
            <p>
              <strong>内容过滤:</strong> 隐藏代码片段标签之间的内容，只显示用户友好的文本
            </p>
            <p>
              <strong>延时刷新:</strong> 流式传输完成后延时500ms调用接口获取最新报告内容
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default StreamingDemo
