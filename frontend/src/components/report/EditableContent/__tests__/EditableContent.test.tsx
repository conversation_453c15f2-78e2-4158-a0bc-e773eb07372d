import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import EditableContent from '../index'

describe('EditableContent', () => {
  const simpleHtmlContent = `
    <div>
      <h1>Simple HTML Content</h1>
      <p>This is a simple paragraph without JavaScript.</p>
    </div>
  `

  const complexHtmlContent = `
    <!DOCTYPE html>
    <html>
    <head>
      <script src="https://unpkg.com/echarts@5.6.0/dist/echarts.min.js"></script>
    </head>
    <body>
      <div id="chart" style="width: 400px; height: 300px;"></div>
      <script>
        const chart = echarts.init(document.getElementById('chart'));
        chart.setOption({
          title: { text: 'Test Chart' },
          xAxis: { type: 'category', data: ['A', 'B', 'C'] },
          yAxis: { type: 'value' },
          series: [{ data: [120, 200, 150], type: 'bar' }]
        });
      </script>
    </body>
    </html>
  `

  describe('渲染模式检测', () => {
    it('应该为简单 HTML 内容使用 simple 模式', () => {
      render(
        <EditableContent 
          content={simpleHtmlContent} 
          renderMode="auto"
        />
      )
      
      // 简单模式应该直接渲染内容
      expect(screen.getByText('Simple HTML Content')).toBeInTheDocument()
      expect(screen.getByText('This is a simple paragraph without JavaScript.')).toBeInTheDocument()
    })

    it('应该为包含 JavaScript 的内容使用 enhanced 模式', () => {
      render(
        <EditableContent 
          content={complexHtmlContent} 
          renderMode="auto"
        />
      )
      
      // enhanced 模式应该使用 iframe
      const iframe = screen.getByTitle('HTML Content Renderer')
      expect(iframe).toBeInTheDocument()
      expect(iframe.tagName).toBe('IFRAME')
    })

    it('应该强制使用指定的渲染模式', () => {
      render(
        <EditableContent 
          content={simpleHtmlContent} 
          renderMode="enhanced"
        />
      )
      
      // 即使是简单内容，也应该使用 iframe
      const iframe = screen.getByTitle('HTML Content Renderer')
      expect(iframe).toBeInTheDocument()
    })
  })

  describe('编辑功能', () => {
    it('应该在编辑模式下显示工具栏', () => {
      render(
        <EditableContent 
          content={complexHtmlContent} 
          editable={true}
          renderMode="enhanced"
        />
      )
      
      // 应该显示工具栏
      expect(screen.getByText('预览模式')).toBeInTheDocument()
      expect(screen.getByText('Ctrl+E: 切换模式 | Ctrl+S: 保存')).toBeInTheDocument()
    })

    it('应该能够切换编辑模式', async () => {
      render(
        <EditableContent 
          content={complexHtmlContent} 
          editable={true}
          renderMode="enhanced"
        />
      )
      
      const toggleButton = screen.getByText('预览模式')
      
      // 点击切换到编辑模式
      fireEvent.click(toggleButton)
      
      await waitFor(() => {
        expect(screen.getByText('编辑模式')).toBeInTheDocument()
        expect(screen.getByPlaceholderText('在此输入 HTML 内容...')).toBeInTheDocument()
      })
    })

    it('应该在编辑模式下显示 HTML 源码', async () => {
      render(
        <EditableContent 
          content={complexHtmlContent} 
          editable={true}
          renderMode="enhanced"
        />
      )
      
      const toggleButton = screen.getByText('预览模式')
      fireEvent.click(toggleButton)
      
      await waitFor(() => {
        const textarea = screen.getByPlaceholderText('在此输入 HTML 内容...')
        expect(textarea).toHaveValue(complexHtmlContent)
      })
    })
  })

  describe('内容变化处理', () => {
    it('应该在内容变化时调用 onChange 回调', async () => {
      const mockOnChange = jest.fn()
      
      render(
        <EditableContent 
          content={complexHtmlContent} 
          editable={true}
          renderMode="enhanced"
          onChange={mockOnChange}
          debounceDelay={100}
        />
      )
      
      // 切换到编辑模式
      const toggleButton = screen.getByText('预览模式')
      fireEvent.click(toggleButton)
      
      await waitFor(() => {
        const textarea = screen.getByPlaceholderText('在此输入 HTML 内容...')
        
        // 修改内容
        fireEvent.change(textarea, { 
          target: { value: '<div>Modified content</div>' } 
        })
      })
      
      // 等待防抖延迟
      await waitFor(() => {
        expect(mockOnChange).toHaveBeenCalledWith('<div>Modified content</div>')
      }, { timeout: 200 })
    })
  })

  describe('保存状态显示', () => {
    it('应该显示保存状态指示器', () => {
      render(
        <EditableContent 
          content={simpleHtmlContent} 
          editable={true}
          saveStatus="saving"
        />
      )
      
      expect(screen.getByText('保存中...')).toBeInTheDocument()
    })

    it('应该显示保存成功状态', () => {
      render(
        <EditableContent 
          content={simpleHtmlContent} 
          editable={true}
          saveStatus="saved"
        />
      )
      
      expect(screen.getByText('已保存')).toBeInTheDocument()
    })

    it('应该显示保存错误状态', () => {
      render(
        <EditableContent 
          content={simpleHtmlContent} 
          editable={true}
          saveStatus="error"
        />
      )
      
      expect(screen.getByText('保存失败')).toBeInTheDocument()
    })
  })

  describe('JavaScript 检测', () => {
    const testCases = [
      {
        name: '检测 script 标签',
        content: '<div><script>console.log("test")</script></div>',
        expected: true
      },
      {
        name: '检测事件处理器',
        content: '<button onclick="alert()">Click</button>',
        expected: true
      },
      {
        name: '检测 ECharts 库',
        content: '<script src="echarts.min.js"></script>',
        expected: true
      },
      {
        name: '纯 HTML 内容',
        content: '<div><p>Simple text</p></div>',
        expected: false
      }
    ]

    testCases.forEach(({ name, content, expected }) => {
      it(`应该${expected ? '检测到' : '不检测到'} JavaScript: ${name}`, () => {
        render(
          <EditableContent 
            content={content} 
            renderMode="auto"
          />
        )
        
        if (expected) {
          // 应该使用 iframe 渲染
          expect(screen.queryByTitle('HTML Content Renderer')).toBeInTheDocument()
        } else {
          // 应该使用简单渲染
          expect(screen.queryByTitle('HTML Content Renderer')).not.toBeInTheDocument()
        }
      })
    })
  })

  describe('键盘快捷键', () => {
    it('应该支持 Ctrl+S 保存快捷键', async () => {
      const mockOnChange = jest.fn()
      
      render(
        <EditableContent 
          content={simpleHtmlContent} 
          editable={true}
          onChange={mockOnChange}
        />
      )
      
      const editableDiv = screen.getByRole('textbox')
      
      // 模拟 Ctrl+S
      fireEvent.keyDown(editableDiv, { 
        key: 's', 
        ctrlKey: true 
      })
      
      await waitFor(() => {
        expect(mockOnChange).toHaveBeenCalled()
      })
    })
  })
})
