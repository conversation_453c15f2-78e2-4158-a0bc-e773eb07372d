import React, { useState, useRef, useEffect, useCallback } from 'react'
import clsx from 'clsx'
import WebContainerRenderer from '../WebContainerRenderer'

export interface EditableContentProps {
  /** HTML内容 */
  content: string
  /** 是否启用编辑模式 */
  editable?: boolean
  /** 内容变化回调 */
  onChange?: (content: string) => void
  /** 保存状态 */
  saveStatus?: 'idle' | 'saving' | 'saved' | 'error'
  /** 自定义样式类名 */
  className?: string
  /** 防抖延迟时间（毫秒） */
  debounceDelay?: number
  /** 渲染模式：'simple' 使用 dangerouslySetInnerHTML，'enhanced' 使用 iframe */
  renderMode?: 'simple' | 'enhanced' | 'auto'
  /** iframe 高度（仅在 enhanced 模式下有效） */
  height?: string | number
}

const EditableContent: React.FC<EditableContentProps> = ({
  content,
  editable = false,
  onChange,
  saveStatus = 'idle',
  className = '',
  debounceDelay = 1000,
  renderMode = 'auto',
  height = '600px'
}) => {
  const contentRef = useRef<HTMLDivElement>(null)
  const [isEditing, setIsEditing] = useState(false)
  const [localContent, setLocalContent] = useState(content)
  const debounceTimerRef = useRef<number | undefined>()

  // 检测内容是否包含 JavaScript
  const hasJavaScript = useCallback((htmlContent: string) => {
    const scriptRegex = /<script[\s\S]*?<\/script>/gi
    const onEventRegex = /\son\w+\s*=/gi
    const jsLibRegex = /(echarts|chart\.js|d3\.js|plotly)/gi

    return scriptRegex.test(htmlContent) ||
           onEventRegex.test(htmlContent) ||
           jsLibRegex.test(htmlContent)
  }, [])

  // 确定实际使用的渲染模式
  const actualRenderMode = useCallback(() => {
    if (renderMode === 'auto') {
      return hasJavaScript(content) ? 'enhanced' : 'simple'
    }
    return renderMode
  }, [renderMode, content, hasJavaScript])

  // 同步外部内容变化
  useEffect(() => {
    if (content !== localContent && !isEditing) {
      setLocalContent(content)
      if (contentRef.current) {
        contentRef.current.innerHTML = content
      }
    }
  }, [content, localContent, isEditing])

  // 防抖处理内容变化
  const debouncedOnChange = useCallback((newContent: string) => {
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current)
    }

    debounceTimerRef.current = window.setTimeout(() => {
      if (onChange) {
        onChange(newContent)
      }
    }, debounceDelay)
  }, [onChange, debounceDelay])

  // 立即保存（用于Ctrl+S快捷键）
  const saveImmediately = useCallback(() => {
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current)
    }
    if (contentRef.current && onChange) {
      onChange(contentRef.current.innerHTML)
    }
  }, [onChange])

  // 处理内容编辑
  const handleInput = useCallback(() => {
    if (!contentRef.current || !editable) return

    const newContent = contentRef.current.innerHTML
    setLocalContent(newContent)
    debouncedOnChange(newContent)
  }, [editable, debouncedOnChange])

  // 处理焦点事件
  const handleFocus = useCallback(() => {
    setIsEditing(true)
  }, [])

  const handleBlur = useCallback(() => {
    setIsEditing(false)
  }, [])

  // 处理键盘事件
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (!editable) return

    // 阻止某些快捷键的默认行为
    if (e.ctrlKey || e.metaKey) {
      switch (e.key) {
        case 's':
          e.preventDefault()
          saveImmediately()
          break
        case 'z':
          // 允许撤销操作
          break
        case 'y':
          // 允许重做操作
          break
        default:
          break
      }
    }
  }, [editable, saveImmediately])

  // 清理定时器
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current)
      }
    }
  }, [])

  // 渲染保存状态指示器
  const renderSaveStatus = () => {
    if (!editable || saveStatus === 'idle') return null

    const statusConfig = {
      saving: {
        icon: (
          <svg className="w-4 h-4 animate-spin text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
        ),
        text: '保存中...',
        className: 'text-blue-600 bg-blue-50 border-blue-200'
      },
      saved: {
        icon: (
          <svg className="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        ),
        text: '已保存',
        className: 'text-green-600 bg-green-50 border-green-200'
      },
      error: {
        icon: (
          <svg className="w-4 h-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        ),
        text: '保存失败',
        className: 'text-red-600 bg-red-50 border-red-200'
      }
    }

    const config = statusConfig[saveStatus]
    if (!config) return null

    return (
      <div className={clsx(
        'fixed top-4 right-4 z-50 flex items-center space-x-2 px-3 py-2 rounded-lg border text-sm font-medium shadow-lg',
        config.className
      )}>
        {config.icon}
        <span>{config.text}</span>
      </div>
    )
  }

  // 根据渲染模式选择渲染方式
  const currentMode = actualRenderMode()

  if (currentMode === 'enhanced') {
    return (
      <>
        <WebContainerRenderer
          content={localContent}
          editable={editable}
          onChange={onChange}
          saveStatus={saveStatus}
          className={className}
          debounceDelay={debounceDelay}
          height={height}
        />
        {renderSaveStatus()}
      </>
    )
  }

  // 简单模式：使用原有的 dangerouslySetInnerHTML
  return (
    <>
      <div
        ref={contentRef}
        className={clsx(
          'prose prose-lg max-w-none',
          editable && 'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 rounded-lg',
          editable && isEditing && 'ring-2 ring-blue-300 ring-opacity-50',
          className
        )}
        contentEditable={editable}
        suppressContentEditableWarning={true}
        onInput={handleInput}
        onFocus={handleFocus}
        onBlur={handleBlur}
        onKeyDown={handleKeyDown}
        dangerouslySetInnerHTML={{ __html: localContent }}
        style={{
          minHeight: editable ? '100px' : 'auto',
          padding: editable ? '16px' : '0'
        }}
      />
      {renderSaveStatus()}
    </>
  )
}

export default EditableContent
