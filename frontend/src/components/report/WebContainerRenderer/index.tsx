import React, { useRef, useEffect, useState, useCallback } from 'react'
import clsx from 'clsx'

export interface WebContainerRendererProps {
  /** HTML内容 */
  content: string
  /** 是否启用编辑模式 */
  editable?: boolean
  /** 内容变化回调 */
  onChange?: (content: string) => void
  /** 保存状态 */
  saveStatus?: 'idle' | 'saving' | 'saved' | 'error'
  /** 自定义样式类名 */
  className?: string
  /** 防抖延迟时间（毫秒） */
  debounceDelay?: number
  /** iframe 高度 */
  height?: string | number
}

const WebContainerRenderer: React.FC<WebContainerRendererProps> = ({
  content,
  editable = false,
  onChange,
  saveStatus = 'idle',
  className = '',
  debounceDelay = 1000,
  height = '600px'
}) => {
  const iframeRef = useRef<HTMLIFrameElement>(null)
  const editorRef = useRef<HTMLTextAreaElement>(null)
  const [isEditing, setIsEditing] = useState(false)
  const [localContent, setLocalContent] = useState(content)
  const debounceTimerRef = useRef<NodeJS.Timeout>()

  // 同步外部内容变化
  useEffect(() => {
    if (content !== localContent && !isEditing) {
      setLocalContent(content)
      updateIframeContent(content)
    }
  }, [content, localContent, isEditing])

  // 更新 iframe 内容
  const updateIframeContent = useCallback((htmlContent: string) => {
    if (!iframeRef.current) return

    const iframe = iframeRef.current
    const doc = iframe.contentDocument || iframe.contentWindow?.document

    if (doc) {
      doc.open()
      doc.write(htmlContent)
      doc.close()

      // 监听 iframe 内容变化（如果需要双向绑定）
      if (editable) {
        const handleIframeChange = () => {
          const newContent = doc.documentElement.outerHTML
          if (newContent !== localContent) {
            setLocalContent(newContent)
            debouncedOnChange(newContent)
          }
        }

        // 监听 iframe 内的变化
        doc.addEventListener('input', handleIframeChange)
        doc.addEventListener('change', handleIframeChange)
      }
    }
  }, [editable, localContent])

  // 防抖处理内容变化
  const debouncedOnChange = useCallback((newContent: string) => {
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current)
    }

    debounceTimerRef.current = setTimeout(() => {
      if (onChange) {
        onChange(newContent)
      }
    }, debounceDelay)
  }, [onChange, debounceDelay])

  // 立即保存
  const saveImmediately = useCallback(() => {
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current)
    }
    if (onChange) {
      onChange(localContent)
    }
  }, [onChange, localContent])

  // 处理编辑器内容变化
  const handleEditorChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newContent = e.target.value
    setLocalContent(newContent)
    debouncedOnChange(newContent)
    updateIframeContent(newContent)
  }, [debouncedOnChange, updateIframeContent])

  // 切换编辑模式
  const toggleEditMode = useCallback(() => {
    setIsEditing(!isEditing)
  }, [isEditing])

  // 处理键盘快捷键
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (!editable) return

    if (e.ctrlKey || e.metaKey) {
      switch (e.key) {
        case 's':
          e.preventDefault()
          saveImmediately()
          break
        case 'e':
          e.preventDefault()
          toggleEditMode()
          break
      }
    }
  }, [editable, saveImmediately, toggleEditMode])

  // 初始化 iframe 内容
  useEffect(() => {
    updateIframeContent(localContent)
  }, [updateIframeContent, localContent])

  // 清理定时器
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current)
      }
    }
  }, [])

  // 渲染保存状态指示器
  const renderSaveStatus = () => {
    if (!editable || saveStatus === 'idle') return null

    const statusConfig = {
      saving: {
        icon: (
          <svg className="w-4 h-4 animate-spin text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
        ),
        text: '保存中...',
        className: 'text-blue-600 bg-blue-50 border-blue-200'
      },
      saved: {
        icon: (
          <svg className="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        ),
        text: '已保存',
        className: 'text-green-600 bg-green-50 border-green-200'
      },
      error: {
        icon: (
          <svg className="w-4 h-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        ),
        text: '保存失败',
        className: 'text-red-600 bg-red-50 border-red-200'
      }
    }

    const config = statusConfig[saveStatus]
    if (!config) return null

    return (
      <div className={clsx(
        'fixed top-4 right-4 z-50 flex items-center space-x-2 px-3 py-2 rounded-lg border text-sm font-medium shadow-lg',
        config.className
      )}>
        {config.icon}
        <span>{config.text}</span>
      </div>
    )
  }

  // 渲染工具栏
  const renderToolbar = () => {
    if (!editable) return null

    return (
      <div className="flex items-center justify-between p-2 bg-gray-50 border-b border-gray-200">
        <div className="flex items-center space-x-2">
          <button
            onClick={toggleEditMode}
            className={clsx(
              'px-3 py-1 text-sm rounded-md transition-colors',
              isEditing
                ? 'bg-blue-500 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            )}
          >
            {isEditing ? '预览模式' : '编辑模式'}
          </button>
          <span className="text-xs text-gray-500">
            Ctrl+E: 切换模式 | Ctrl+S: 保存
          </span>
        </div>
        <div className="text-xs text-gray-500">
          {isEditing ? 'HTML 编辑器' : 'JavaScript 渲染预览'}
        </div>
      </div>
    )
  }

  return (
    <div className={clsx('border border-gray-200 rounded-lg overflow-hidden', className)} onKeyDown={handleKeyDown}>
      {renderToolbar()}
      
      <div className="relative" style={{ height }}>
        {isEditing && editable ? (
          <textarea
            ref={editorRef}
            value={localContent}
            onChange={handleEditorChange}
            className="w-full h-full p-4 font-mono text-sm border-none resize-none focus:outline-none"
            placeholder="在此输入 HTML 内容..."
            spellCheck={false}
          />
        ) : (
          <iframe
            ref={iframeRef}
            className="w-full h-full border-none"
            sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"
            title="HTML Content Renderer"
          />
        )}
      </div>
      
      {renderSaveStatus()}
    </div>
  )
}

export default WebContainerRenderer
