import React, { useState, useEffect, useCallback, useRef } from 'react'
import clsx from 'clsx'
import { useTaskStore, useReportStore, useContextStore } from '../../../stores'
import { reportService } from '../../../services/api/reportService'

interface ReportViewerProps {
  className?: string
}

const ReportViewer: React.FC<ReportViewerProps> = ({
  className = ''
}) => {
  const { currentTask } = useTaskStore()
  const { reportHtml, isGenerating, updateReport, startGeneration, finishGeneration } = useReportStore()
  const { addMultipleContext } = useContextStore()

  const iframeRef = useRef<HTMLIFrameElement>(null)
  const [reportContent, setReportContent] = useState('')
  const [selectedElements, setSelectedElements] = useState<any[]>([])
  const [showSelectionPanel, setShowSelectionPanel] = useState(false)
  const [editableElements, setEditableElements] = useState<any[]>([])
  const [editingElement, setEditingElement] = useState<string | null>(null)

  // 处理来自 iframe 的选择消息
  const handleIframeMessage = useCallback((event: MessageEvent) => {
    if (event.data?.type === 'ELEMENT_SELECTED') {
      const selectedData = event.data.data
      console.log('[DEBUG] Elements selected:', selectedData)

      // 所有选中的元素都保存
      setSelectedElements(selectedData)

      // 只有特定类型的元素显示在编辑面板中
      const editableTypes = ['img', 'p', 'article', 'table', 'span', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6']
      const editableData = selectedData.filter((element: any) =>
        editableTypes.includes(element.tagName.toLowerCase())
      )
      setEditableElements(editableData)
      setShowSelectionPanel(editableData.length > 0)

      // 将所有选中的内容添加到对话上下文
      if (selectedData.length > 0) {
        const contextItems = selectedData.map((element: any) => ({
          type: 'html_element' as const,
          tagName: element.tagName,
          className: element.className,
          elementId: element.id,
          content: element.outerHTML,
          textContent: element.textContent,
          summary: `${element.tagName.toUpperCase()} 元素${element.className ? ` (${element.className.split(' ')[0]})` : ''}`,
          source: 'selection' as const
        }))

        // 添加到上下文管理器
        addMultipleContext(contextItems)
        console.log('[DEBUG] Added to context store:', contextItems.length, 'items')
        console.log('[DEBUG] Editable elements:', editableData.length, 'items')
      }
    }
  }, [addMultipleContext])

  // 保存修改后的 HTML 内容
  const saveHtmlContent = useCallback(async (updatedHtml: string) => {
    if (!currentTask?.id) {
      console.error('[DEBUG] No current task ID for saving')
      return false
    }

    try {
      const response = await fetch(`/api/tasks/${currentTask.id}/report`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: updatedHtml
        })
      })

      if (response.ok) {
        console.log('[DEBUG] HTML content saved successfully')
        // 更新本地状态
        updateReport(updatedHtml)
        return true
      } else {
        console.error('[DEBUG] Failed to save HTML content:', response.status)
        return false
      }
    } catch (error) {
      console.error('[DEBUG] Error saving HTML content:', error)
      return false
    }
  }, [currentTask?.id, updateReport])

  // 获取清理后的 HTML 内容
  const getCleanHtmlFromIframe = useCallback((): Promise<string> => {
    return new Promise((resolve, reject) => {
      if (!iframeRef.current) {
        reject(new Error('Iframe not available'))
        return
      }

      const requestId = `clean-html-${Date.now()}`

      // 设置超时
      const timeout = setTimeout(() => {
        window.removeEventListener('message', messageHandler)
        reject(new Error('Timeout waiting for clean HTML'))
      }, 5000)

      // 消息处理器
      const messageHandler = (event: MessageEvent) => {
        if (event.data?.type === 'CLEAN_HTML_RESPONSE' && event.data?.requestId === requestId) {
          clearTimeout(timeout)
          window.removeEventListener('message', messageHandler)
          resolve(event.data.data)
        }
      }

      // 监听响应
      window.addEventListener('message', messageHandler)

      // 发送请求到 iframe
      const iframeWindow = (iframeRef.current as any).contentWindow
      if (iframeWindow) {
        iframeWindow.postMessage({
          type: 'GET_CLEAN_HTML',
          requestId: requestId
        }, '*')
      } else {
        clearTimeout(timeout)
        window.removeEventListener('message', messageHandler)
        reject(new Error('Cannot access iframe window'))
      }
    })
  }, [])

  // 处理元素文本编辑
  const handleElementEdit = useCallback(async (elementId: string, newTextContent: string) => {
    if (!iframeRef.current) return

    const iframe = iframeRef.current as HTMLIFrameElement
    const doc = (iframe as any).contentDocument || (iframe as any).contentWindow?.document

    if (doc) {
      // 在 iframe 中找到对应元素并更新
      const element = doc.querySelector(`[data-element-id="${elementId}"]`)
      if (element) {
        element.textContent = newTextContent

        try {
          // 获取清理后的 HTML（不包含高亮样式）
          const cleanHtml = await getCleanHtmlFromIframe()

          // 保存到服务器
          const saved = await saveHtmlContent(cleanHtml)
          if (saved) {
            // 更新本地选中元素的数据
            setEditableElements(prev => prev.map(el =>
              el.elementId === elementId
                ? { ...el, textContent: newTextContent, fullTextContent: newTextContent }
                : el
            ))

            console.log('[DEBUG] Element updated and saved:', elementId)
          }
        } catch (error) {
          console.error('[DEBUG] Failed to get clean HTML:', error)
          // 降级方案：直接使用当前 HTML（但会包含高亮样式）
          const fallbackHtml = doc.documentElement.outerHTML
          await saveHtmlContent(fallbackHtml)
        }
      }
    }
  }, [saveHtmlContent, getCleanHtmlFromIframe])

  // 监听 iframe 消息
  useEffect(() => {
    window.addEventListener('message', handleIframeMessage)
    return () => {
      window.removeEventListener('message', handleIframeMessage)
    }
  }, [handleIframeMessage])

  // 监听键盘事件，处理 ESC 键关闭弹窗
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && showSelectionPanel) {
        event.preventDefault()
        setShowSelectionPanel(false)
        setEditingElement(null) // 同时取消编辑状态
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => {
      document.removeEventListener('keydown', handleKeyDown)
    }
  }, [showSelectionPanel])

  // 监听报告生成事件
  useEffect(() => {
    const handleReportGenerationStart = () => {
      console.log('[DEBUG] ReportViewer: Report generation started')
      startGeneration()
      setReportContent('')
      // 清空之前的报告内容
      updateReport('')
      console.log('[DEBUG] ReportViewer: Cleared previous report content')
    }

    const handleReportGenerationEnd = (event: CustomEvent) => {
      console.log('[DEBUG] ReportViewer: Report generation ended:', event.detail.reportPath)
      finishGeneration(event.detail.reportPath)

      // 从文件路径加载报告内容
      if (event.detail.reportPath) {
        console.log('[DEBUG] ReportViewer: Loading report from path:', event.detail.reportPath)
        loadReportFromPath(event.detail.reportPath)
      } else {
        console.log('[DEBUG] ReportViewer: No report path provided')
      }
    }

    console.log('[DEBUG] ReportViewer: Setting up event listeners')
    window.addEventListener('reportGenerationStart', handleReportGenerationStart)
    window.addEventListener('reportGenerationEnd', handleReportGenerationEnd as EventListener)

    return () => {
      console.log('[DEBUG] ReportViewer: Cleaning up event listeners')
      window.removeEventListener('reportGenerationStart', handleReportGenerationStart)
      window.removeEventListener('reportGenerationEnd', handleReportGenerationEnd as EventListener)
    }
  }, [startGeneration, updateReport, finishGeneration])

  // 注入选择功能脚本
  const injectSelectionScript = useCallback((doc: Document) => {
    const script = doc.createElement('script')
    script.textContent = `
      (function() {
        let isSelecting = false;
        let startX, startY;
        let selectionBox = null;
        let selectedElements = [];
        let highlightedElements = [];

        // 创建选择框
        function createSelectionBox(x, y) {
          const box = document.createElement('div');
          box.style.cssText = \`
            position: fixed;
            border: 2px dashed #3b82f6;
            background: rgba(59, 130, 246, 0.1);
            pointer-events: none;
            z-index: 10000;
            left: \${x}px;
            top: \${y}px;
            width: 0;
            height: 0;
          \`;
          document.body.appendChild(box);
          return box;
        }

        // 更新选择框大小
        function updateSelectionBox(box, startX, startY, currentX, currentY) {
          const left = Math.min(startX, currentX);
          const top = Math.min(startY, currentY);
          const width = Math.abs(currentX - startX);
          const height = Math.abs(currentY - startY);

          box.style.left = left + 'px';
          box.style.top = top + 'px';
          box.style.width = width + 'px';
          box.style.height = height + 'px';
        }

        // 检查元素是否在选择区域内
        function isElementInSelection(element, selectionRect) {
          const rect = element.getBoundingClientRect();
          return !(rect.right < selectionRect.left ||
                   rect.left > selectionRect.right ||
                   rect.bottom < selectionRect.top ||
                   rect.top > selectionRect.bottom);
        }

        // 高亮元素
        function highlightElement(element) {
          element.style.outline = '3px solid #3b82f6';
          element.style.outlineOffset = '2px';
          element.style.backgroundColor = 'rgba(59, 130, 246, 0.05)';
          element.setAttribute('data-selected', 'true');
        }

        // 清除高亮
        function clearHighlight() {
          highlightedElements.forEach(element => {
            element.style.outline = '';
            element.style.outlineOffset = '';
            element.style.backgroundColor = '';
            element.removeAttribute('data-selected');
          });
          highlightedElements = [];
        }

        // 清除所有高亮样式（用于保存前清理）
        function clearAllHighlightStyles() {
          const allHighlighted = document.querySelectorAll('[data-selected="true"]');
          allHighlighted.forEach(element => {
            element.style.outline = '';
            element.style.outlineOffset = '';
            element.style.backgroundColor = '';
            element.removeAttribute('data-selected');
          });
        }

        // 获取清理后的 HTML 内容
        function getCleanHtmlContent() {
          // 临时清除所有高亮样式
          const highlightedElements = document.querySelectorAll('[data-selected="true"]');
          const originalStyles = [];

          // 保存原始样式
          highlightedElements.forEach(element => {
            originalStyles.push({
              element: element,
              outline: element.style.outline,
              outlineOffset: element.style.outlineOffset,
              backgroundColor: element.style.backgroundColor,
              hasSelectedAttr: element.hasAttribute('data-selected')
            });

            // 清除高亮样式
            element.style.outline = '';
            element.style.outlineOffset = '';
            element.style.backgroundColor = '';
            element.removeAttribute('data-selected');
          });

          // 获取清理后的 HTML
          const cleanHtml = document.documentElement.outerHTML;

          // 恢复高亮样式
          originalStyles.forEach(styleInfo => {
            styleInfo.element.style.outline = styleInfo.outline;
            styleInfo.element.style.outlineOffset = styleInfo.outlineOffset;
            styleInfo.element.style.backgroundColor = styleInfo.backgroundColor;
            if (styleInfo.hasSelectedAttr) {
              styleInfo.element.setAttribute('data-selected', 'true');
            }
          });

          return cleanHtml;
        }

        // 获取可选择的元素
        function getSelectableElements() {
          return Array.from(document.querySelectorAll('div, img, p, span, section, article, header, footer, main, aside, nav, figure, blockquote, pre, table, ul, ol, li, h1, h2, h3, h4, h5, h6'));
        }

        // 鼠标按下事件
        document.addEventListener('mousedown', function(e) {
          // 只在按住 Ctrl/Cmd 键时启用选择
          if (!e.ctrlKey && !e.metaKey) return;

          e.preventDefault();
          isSelecting = true;
          startX = e.clientX;
          startY = e.clientY;

          clearHighlight();
          selectionBox = createSelectionBox(startX, startY);
        });

        // 过滤掉具有包含关系的父元素，只保留最深层的子元素
        function filterDeepestElements(elements) {
          const filtered = [];

          for (let i = 0; i < elements.length; i++) {
            const currentElement = elements[i];
            let isDeepest = true;

            // 检查当前元素是否包含其他任何元素
            for (let j = 0; j < elements.length; j++) {
              if (i !== j) {
                const otherElement = elements[j];
                // 如果当前元素包含其他元素，则当前元素不是最深层的
                if (currentElement.contains(otherElement)) {
                  isDeepest = false;
                  break;
                }
              }
            }

            if (isDeepest) {
              filtered.push(currentElement);
            }
          }

          return filtered;
        }

        // 鼠标移动事件
        document.addEventListener('mousemove', function(e) {
          if (!isSelecting || !selectionBox) return;

          e.preventDefault();
          updateSelectionBox(selectionBox, startX, startY, e.clientX, e.clientY);

          // 实时高亮选中的元素
          clearHighlight();
          const selectionRect = {
            left: Math.min(startX, e.clientX),
            top: Math.min(startY, e.clientY),
            right: Math.max(startX, e.clientX),
            bottom: Math.max(startY, e.clientY)
          };

          const selectableElements = getSelectableElements();
          const intersectingElements = [];

          // 先收集所有相交的元素
          selectableElements.forEach(element => {
            if (isElementInSelection(element, selectionRect)) {
              intersectingElements.push(element);
            }
          });

          // 过滤出最深层的元素
          const deepestElements = filterDeepestElements(intersectingElements);

          // 只高亮最深层的元素
          deepestElements.forEach(element => {
            highlightElement(element);
            highlightedElements.push(element);
          });
        });

        // 鼠标释放事件
        document.addEventListener('mouseup', function(e) {
          if (!isSelecting) return;

          isSelecting = false;

          if (selectionBox) {
            selectionBox.remove();
            selectionBox = null;
          }

          // 收集选中元素的信息
          if (highlightedElements.length > 0) {
            const selectedData = highlightedElements.map((element, index) => {
              // 为可编辑元素添加唯一标识
              const editableTypes = ['img', 'p', 'article', 'header', 'table', 'span', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'];
              const isEditable = editableTypes.includes(element.tagName.toLowerCase());

              if (isEditable && !element.getAttribute('data-element-id')) {
                const elementId = \`editable-\${Date.now()}-\${index}\`;
                element.setAttribute('data-element-id', elementId);
              }

              return {
                tagName: element.tagName.toLowerCase(),
                className: element.className,
                id: element.id,
                elementId: element.getAttribute('data-element-id'),
                innerHTML: element.innerHTML,
                outerHTML: element.outerHTML,
                textContent: element.textContent?.substring(0, 200) + (element.textContent?.length > 200 ? '...' : ''),
                fullTextContent: element.textContent,
                rect: element.getBoundingClientRect(),
                isEditable: isEditable
              };
            });

            // 发送消息到父窗口
            if (window.parent !== window) {
              window.parent.postMessage({
                type: 'ELEMENT_SELECTED',
                data: selectedData
              }, '*');
            }

            console.log('Selected elements:', selectedData);
          }
        });

        // 键盘事件 - ESC 取消选择
        document.addEventListener('keydown', function(e) {
          if (e.key === 'Escape') {
            clearHighlight();
            if (selectionBox) {
              selectionBox.remove();
              selectionBox = null;
            }
            isSelecting = false;
          }
        });

        // 添加提示信息
        const hint = document.createElement('div');
        hint.style.cssText = \`
          position: fixed;
          top: 10px;
          right: 10px;
          background: rgba(0, 0, 0, 0.8);
          color: white;
          padding: 8px 12px;
          border-radius: 4px;
          font-size: 12px;
          z-index: 10001;
          pointer-events: none;
        \`;
        hint.textContent = '按住 Ctrl/Cmd + 拖拽鼠标选择区域';
        document.body.appendChild(hint);

        // 监听来自父窗口的消息
        window.addEventListener('message', function(event) {
          if (event.data?.type === 'GET_CLEAN_HTML') {
            const cleanHtml = getCleanHtmlContent();
            event.source.postMessage({
              type: 'CLEAN_HTML_RESPONSE',
              data: cleanHtml,
              requestId: event.data.requestId
            }, '*');
          }
        });

        console.log('Selection functionality injected successfully');
      })();
    `;
    doc.head.appendChild(script)
  }, [])

  // 更新 iframe 内容
  const updateIframeContent = useCallback((htmlContent: string) => {
    if (!iframeRef.current) return

    const iframe = iframeRef.current as HTMLIFrameElement

    // 方法1：使用 srcdoc 属性（推荐）
    if ('srcdoc' in iframe) {
      iframe.srcdoc = htmlContent
      console.log('[DEBUG] HTML content set via srcdoc')

      // 等待 iframe 加载完成后注入脚本
      iframe.onload = () => {
        const doc = (iframe as any).contentDocument || (iframe as any).contentWindow?.document
        if (doc) {
          injectSelectionScript(doc)
        }
      }
      return
    }

    // 方法2：直接写入文档（备用）
    const doc = (iframe as any).contentDocument || (iframe as any).contentWindow?.document
    if (doc) {
      doc.open()
      doc.write(htmlContent)
      doc.close()
      console.log('[DEBUG] HTML content written to iframe document')

      // 注入选择脚本
      setTimeout(() => {
        injectSelectionScript(doc)
      }, 100)
    }
  }, [injectSelectionScript])

  // 加载报告内容的函数
  const loadReportContent = useCallback(async (taskId: string) => {
    try {
      console.log('[DEBUG] ReportViewer: Loading report content for task:', taskId)

      const response = await reportService.getReportContent(taskId)

      if (response.success && response.data) {
        console.log('[DEBUG] ReportViewer: Successfully loaded report content')
        const content = response.data.content
        setReportContent(content)

        // 同时更新store中的内容
        updateReport(content)

        // 更新 iframe 内容
        updateIframeContent(content)
      } else {
        console.log('[DEBUG] ReportViewer: No report content found for task:', taskId)
        // 如果没有报告内容，清空显示
        setReportContent('')
        updateReport('')
        updateIframeContent('')
      }
    } catch (error) {
      console.error('[DEBUG] ReportViewer: Failed to load report content:', error)
      // 发生错误时也清空显示
      setReportContent('')
      updateReport('')
      updateIframeContent('')
    }
  }, [updateReport, updateIframeContent])

  // 从文件路径加载报告内容
  const loadReportFromPath = useCallback(async (reportPath: string) => {
    try {
      console.log('[DEBUG] ReportViewer: Starting to load report from path:', reportPath)

      // 从报告路径提取任务ID
      // 路径格式: "task_id/report.html"
      const pathParts = reportPath.split('/')
      const taskId = pathParts[0] // 第一个部分是任务ID

      console.log('[DEBUG] ReportViewer: Extracted taskId:', taskId)

      // 使用API加载报告内容
      await loadReportContent(taskId)
      console.log('[DEBUG] ReportViewer: Report loaded and updated successfully via API')
    } catch (error) {
      console.error('[DEBUG] ReportViewer: Error loading report:', error)
    }
  }, [loadReportContent])

  // 当任务切换时加载新的报告内容
  useEffect(() => {
    // 根据当前任务加载报告内容
    if (currentTask?.id) {
      loadReportContent(currentTask.id)
    } else {
      setReportContent('')
      updateReport('')
      updateIframeContent('')
    }
  }, [currentTask?.id, loadReportContent, updateReport, updateIframeContent])

  // 同步reportHtml到iframe
  useEffect(() => {
    if (reportHtml) {
      updateIframeContent(reportHtml)
    }
  }, [reportHtml, updateIframeContent])

  // 监听报告内容刷新事件
  useEffect(() => {
    const handleReportContentRefresh = (event: CustomEvent) => {
      const { taskId } = event.detail
      console.log('[DEBUG] ReportViewer: Received report content refresh event for task:', taskId)

      if (taskId === currentTask?.id) {
        // 刷新当前任务的报告内容
        loadReportContent(taskId)
      }
    }

    window.addEventListener('reportContentRefresh', handleReportContentRefresh as EventListener)

    return () => {
      window.removeEventListener('reportContentRefresh', handleReportContentRefresh as EventListener)
    }
  }, [currentTask?.id, loadReportContent])

  // 使用合适的内容源
  const displayContent = reportHtml || reportContent





  if (!currentTask) {
    return (
      <div className={clsx('flex flex-col items-center justify-center h-full bg-gray-50', className)}>
        <div className="text-center max-w-md">
          <div className="w-20 h-20 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-6">
            <svg className="w-10 h-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              />
            </svg>
          </div>
          <h3 className="text-xl font-semibold text-gray-900 mb-3">
            选择任务查看报告
          </h3>
          <p className="text-gray-600 leading-relaxed">
            请从左侧任务列表中选择一个任务，或创建新任务开始生成报告。
            生成的报告将在此处显示，您可以选择报告中的内容与AI进行讨论。
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className={clsx('flex flex-col h-full bg-white', className)}>
      {/* 报告内容 */}
      <div className="flex-1 overflow-hidden relative">
        {displayContent ? (
          <>
            <iframe
              ref={iframeRef}
              className="w-full h-full border-none"
              title="Report Content"
              style={{ border: 'none' }}
            />

            {/* 选择面板 */}
            {showSelectionPanel && editableElements.length > 0 && (
              <div className="absolute top-4 right-4 w-96 bg-white border border-gray-200 rounded-lg shadow-lg z-10 max-h-96 overflow-hidden">
                <div className="bg-gray-50 px-4 py-3 border-b border-gray-200 flex items-center justify-between">
                  <div>
                    <p className="text-xs text-gray-500">
                      总共选中 {selectedElements.length} 个元素，已添加到上下文
                    </p>
                  </div>
                  <button
                    onClick={() => setShowSelectionPanel(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                <div className="p-4 overflow-y-auto max-h-80">
                  {editableElements.map((element, index) => (
                    <div key={element.elementId || index} className="mb-4 last:mb-0 border border-gray-200 rounded-lg p-3">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-xs font-medium text-blue-600 bg-blue-50 px-2 py-1 rounded">
                          {element.tagName.toUpperCase()}
                        </span>
                      </div>

                      {/* 可编辑文本区域 */}
                      {element.tagName !== 'img' ? (
                        <div className="mb-2">
                          {editingElement === element.elementId ? (
                            <div className="space-y-2">
                              <textarea
                                defaultValue={element.fullTextContent || element.textContent}
                                className="w-full px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                rows={3}
                                onBlur={(e) => {
                                  const newText = e.target.value
                                  if (newText !== element.fullTextContent && element.elementId) {
                                    handleElementEdit(element.elementId, newText)
                                  }
                                  setEditingElement(null)
                                }}
                                onKeyDown={(e) => {
                                  if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                                    e.preventDefault()
                                    e.currentTarget.blur()
                                  }
                                  if (e.key === 'Escape') {
                                    e.preventDefault()
                                    setEditingElement(null)
                                  }
                                }}
                                autoFocus
                              />
                              <div className="text-xs text-gray-500">
                                按 Cmd/Ctrl+Enter 保存，ESC 取消
                              </div>
                            </div>
                          ) : (
                            <div
                              className="text-xs text-gray-700 p-2 bg-gray-50 rounded cursor-pointer hover:bg-gray-100 transition-colors"
                              onClick={() => setEditingElement(element.elementId)}
                            >
                              {element.fullTextContent || element.textContent || '(无文本内容)'}
                            </div>
                          )}
                        </div>
                      ) : (
                        <div className="mb-2">
                          <div className="text-xs text-gray-600 p-2 bg-gray-50 rounded">
                            {element.outerHTML.includes('src=')
                              ? `图片: ${element.outerHTML.match(/src="([^"]*)"/)?.at(1) || '未知'}`
                              : '图片元素'
                            }
                          </div>
                        </div>
                      )}
                    </div>
                  ))}

                  <div className="mt-4 pt-4 border-t border-gray-200 space-y-2">
                    <div className="text-xs text-green-600 bg-green-50 px-2 py-1 rounded">
                      ✅ 所有选中元素已添加到对话上下文 ({selectedElements.length} 个)
                    </div>
                  </div>
                </div>
              </div>
            )}
          </>
        ) : isGenerating ? (
          <div className="flex flex-col items-center justify-center h-full text-center">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4 animate-pulse">
              <svg className="w-8 h-8 text-blue-600 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              正在生成报告
            </h3>
            <p className="text-gray-600 max-w-md">
              AI正在为您生成详细的数据分析报告，请稍候...
            </p>
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center h-full text-center">
            <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mb-4">
              <svg className="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              报告生成中
            </h3>
            <p className="text-gray-600 max-w-md">
              当前任务还没有生成报告。请在右侧聊天区域描述您的需求，AI将为您生成详细的数据分析报告。
            </p>
          </div>
        )}
      </div>
    </div>
  )
}

export default ReportViewer
