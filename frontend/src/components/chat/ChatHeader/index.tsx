import React from 'react'
import clsx from 'clsx'
import type { ConnectionStatus } from '../../../services/http/chatService'

interface ChatHeaderProps {
  taskTitle: string
  isConnected: boolean
  connectionStatus: ConnectionStatus
  className?: string
}

const ChatHeader: React.FC<ChatHeaderProps> = ({
  taskTitle,
  connectionStatus,
  className = ''
}) => {
  const getStatusColor = () => {
    switch (connectionStatus) {
      case 'connected':
        return 'bg-green-500'
      case 'connecting':
        return 'bg-yellow-500 animate-pulse'
      case 'processing':
        return 'bg-blue-500 animate-pulse'
      case 'idle':
        return 'bg-gray-400'
      case 'error':
        return 'bg-red-500'
      default:
        return 'bg-gray-400'
    }
  }

  const getStatusText = () => {
    switch (connectionStatus) {
      case 'connected':
        return '已连接'
      case 'connecting':
        return '连接中'
      case 'processing':
        return '处理中'
      case 'idle':
        return '未连接'
      case 'error':
        return '连接错误'
      default:
        return '未知状态'
    }
  }

  return (
    <div className={clsx('flex items-center justify-between p-4 border-b border-gray-200 bg-gradient-to-r from-green-50 to-blue-50', className)}>
      {/* 左侧：任务信息 */}
      <div className="flex items-center space-x-3 min-w-0 flex-1">
        {/* AI助手头像 */}
        <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center shadow-lg">
          <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
            />
          </svg>
        </div>

        {/* 任务标题和状态 */}
        <div className="min-w-0 flex-1">
          <h2 className="text-sm font-semibold text-gray-900 truncate" title={taskTitle}>
            {taskTitle}
          </h2>
          <div className="flex items-center space-x-2 mt-1">
            <div className={clsx('w-2 h-2 rounded-full', getStatusColor())}></div>
            <span className="text-xs text-gray-500">{getStatusText()}</span>
          </div>
        </div>
      </div>

      {/* 右侧：操作按钮 */}
      <div className="flex items-center space-x-2">
        {/* 清空聊天记录 */}
        <button
          className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
          title="清空聊天记录"
        >
          <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
            />
          </svg>
        </button>

        {/* 更多选项 */}
        <button
          className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
          title="更多选项"
        >
          <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"
            />
          </svg>
        </button>
      </div>
    </div>
  )
}

export default ChatHeader
