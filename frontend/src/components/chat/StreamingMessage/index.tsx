import React from 'react'
import clsx from 'clsx'
import { useChatStore } from '../../../stores'

interface StreamingMessageProps {
  className?: string
}

const StreamingMessage: React.FC<StreamingMessageProps> = ({
  className = ''
}) => {
  const {
    streamingDisplayText,
    isCodeSnippetMode,
    isStreamActive
  } = useChatStore()

  // 如果没有活跃的流式传输，不显示组件
  if (!isStreamActive) {
    return null
  }

  return (
    <div className={clsx('flex justify-start', className)}>
      <div className="flex max-w-[80%] space-x-3">
        {/* AI助手头像 */}
        <div className="flex-shrink-0">
          <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
            <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
              />
            </svg>
          </div>
        </div>

        {/* 消息内容 */}
        <div className="flex flex-col space-y-1 w-full">
          {/* 消息气泡 */}
          <div className="bg-gray-100 text-gray-900 px-4 py-2 rounded-2xl">
            <div className="text-sm leading-relaxed whitespace-pre-wrap">
              {streamingDisplayText}
              {isStreamActive && (
                <span className="inline-block w-2 h-4 bg-gray-400 animate-pulse ml-1"></span>
              )}
            </div>

            {/* 报告生成中指示器 */}
            {isCodeSnippetMode && (
              <div className="mt-2 flex items-center space-x-2 text-xs text-blue-600">
                <div className="w-3 h-3 border border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                <span>报告生成中...</span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default StreamingMessage
