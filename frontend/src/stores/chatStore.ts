import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'
import { httpChatService, type ConnectionStatus, type TaskStatusData, type MessageData } from '../services/http/chatService'
import type { Message, TaskStatus, StreamChunk } from '../types/api'



// 聊天Store接口
interface ChatStore {
  // 状态
  messages: Message[]
  isConnected: boolean
  connectionStatus: ConnectionStatus
  taskStatus: TaskStatus
  progress: number
  error: string | null
  isProcessing: boolean
  currentTaskId: string | null
  currentStreamChunk: StreamChunk | null

  // 新增流式消息状态
  streamingBuffer: string
  streamingDisplayText: string
  isCodeSnippetMode: boolean
  isStreamActive: boolean

  // 操作
  connect: (taskId: string) => Promise<void>
  disconnect: () => void
  sendMessage: (content: string, contextHtml?: string) => Promise<void>
  clearMessages: () => void
  loadTaskMessages: (taskId: string) => Promise<void>
  cancelProcessing: () => Promise<void>

  // 新增流式消息操作
  updateStreamingBuffer: (chunk: string) => void
  setCodeSnippetMode: (isActive: boolean) => void
  completeStreaming: () => void
  refreshReportContent: () => Promise<void>
  resetStreamingState: () => void
}

// 全局HTTP服务监听器设置（只设置一次）
let isHttpListenersSetup = false

export const useChatStore = create<ChatStore>()(
  devtools(
    immer((set, get) => {
      // 只在第一次创建Store时设置监听器
      if (!isHttpListenersSetup) {
        isHttpListenersSetup = true

        // 设置HTTP服务监听器
        httpChatService.onStatusChange((status: TaskStatusData) => {
          console.log('[DEBUG] ChatStore received status update:', status)
          set((state) => {
            state.taskStatus = status.status
            state.progress = status.progress
            state.isProcessing = status.is_processing
            if (status.error_message) {
              state.error = status.error_message
            }
          })
        })

        httpChatService.onMessageUpdate((messages: MessageData[]) => {
          console.log('[DEBUG] ChatStore received message update:', messages.length, 'messages')
          set((state) => {
            // 转换消息格式
            state.messages = messages.map(msg => ({
              id: msg.id,
              role: msg.role as 'user' | 'assistant',
              content: msg.content,
              timestamp: msg.timestamp,
              has_code_snippet: msg.has_code_snippet
            })).reverse() // 反转顺序，最新的在后面
          })
        })

        httpChatService.onConnectionChange((status: ConnectionStatus) => {
          console.log('[DEBUG] ChatStore received connection change:', status)
          set((state) => {
            state.connectionStatus = status
            state.isConnected = status === 'connected' || status === 'processing'
          })
        })

        httpChatService.onError((error: string) => {
          console.log('[DEBUG] ChatStore received error:', error)
          set((state) => {
            state.error = error
          })
        })

        // 监听流式响应块
        httpChatService.onStreamChunk((chunk: StreamChunk) => {
          console.log('[DEBUG] ChatStore received stream chunk:', chunk)
          const store = get()

          set((state) => {
            state.currentStreamChunk = chunk

            // 根据流式响应类型更新状态
            if (chunk.progress !== undefined) {
              state.progress = chunk.progress
            }

            // 如果是错误类型，设置错误信息
            if (chunk.type === 'error') {
              state.error = chunk.content
            }

            // 处理assistant_chunk类型的流式数据
            if (chunk.type === 'assistant_chunk') {
              state.isStreamActive = true
            }
          })

          // 在set之外处理缓冲区更新
          if (chunk.type === 'assistant_chunk') {
            store.updateStreamingBuffer(chunk.content)
          } else if (chunk.type === 'completed' || chunk.type === 'end') {
            store.completeStreaming()
          }
        })
      }

      return {
        // 初始状态
        messages: [],
        isConnected: false,
        connectionStatus: 'idle' as ConnectionStatus,
        taskStatus: 'idle' as TaskStatus,
        progress: 0,
        error: null,
        isProcessing: false,
        currentTaskId: null,
        currentStreamChunk: null,

        // 新增流式消息状态
        streamingBuffer: '',
        streamingDisplayText: '',
        isCodeSnippetMode: false,
        isStreamActive: false,

        // 发送消息
        sendMessage: async (content, contextHtml) => {
          try {
            // 重置流式传输状态
            get().resetStreamingState()
            await httpChatService.sendMessage(content, contextHtml)
          } catch (error) {
            console.error('[ChatStore] Failed to send message:', error)
            set((state) => {
              state.error = error instanceof Error ? error.message : 'Failed to send message'
            })
          }
        },

        // 连接到任务
        connect: async (taskId: string) => {
          console.log(`[ChatStore] Connecting to task: ${taskId}`)

          // 清理之前的状态
          set((state) => {
            state.messages = []
            state.error = null
            state.currentTaskId = taskId
          })

          try {
            // 先加载历史消息
            await get().loadTaskMessages(taskId)

            // 连接HTTP服务
            await httpChatService.connect(taskId)

            console.log(`[ChatStore] Successfully connected to task: ${taskId}`)
          } catch (error) {
            console.error(`[ChatStore] Failed to connect to task ${taskId}:`, error)
            set((state) => {
              state.error = error instanceof Error ? error.message : 'Connection failed'
            })
          }
        },

        // 断开连接
        disconnect: () => {
          httpChatService.disconnect()
          set((state) => {
            state.currentTaskId = null
            state.isConnected = false
            state.connectionStatus = 'idle'
          })
        },

        // 加载任务的历史消息
        loadTaskMessages: async (taskId: string) => {
          try {
            console.log(`Loading messages for task: ${taskId}`)
            const messages = await httpChatService.getTaskMessages(taskId)
            console.log(`Loaded ${messages.length} messages for task: ${taskId}`)

            // 消息已经在HTTP服务的监听器中处理了，这里不需要手动设置
          } catch (error) {
            console.error('Failed to load task messages:', error)
            set((state) => {
              state.error = error instanceof Error ? error.message : 'Failed to load messages'
            })
          }
        },

        // 清理聊天数据
        clearMessages: () => {
          set((state) => {
            state.messages = []
            state.error = null
          })
        },

        // 取消处理
        cancelProcessing: async () => {
          const { currentTaskId } = get()
          if (currentTaskId) {
            try {
              await httpChatService.cancelTask(currentTaskId)
            } catch (error) {
              console.error('Failed to cancel task:', error)
              set((state) => {
                state.error = error instanceof Error ? error.message : 'Failed to cancel task'
              })
            }
          }
        },

        // 更新流式缓冲区
        updateStreamingBuffer: (chunk: string) => {
          set((state) => {
            state.streamingBuffer += chunk

            // 检查是否包含<code_snippet>标识
            const codeSnippetStartIndex = state.streamingBuffer.indexOf('<code_snippet>')
            const codeSnippetEndIndex = state.streamingBuffer.indexOf('</code_snippet>')

            if (codeSnippetStartIndex !== -1 && !state.isCodeSnippetMode) {
              // 发现代码片段开始标识
              state.isCodeSnippetMode = true
              // 只显示标识前的内容，保留20个字符的缓冲
              const textBeforeCode = state.streamingBuffer.substring(0, codeSnippetStartIndex)
              state.streamingDisplayText = textBeforeCode.length > 20
                ? textBeforeCode.substring(0, textBeforeCode.length - 20)
                : ''
            } else if (codeSnippetEndIndex !== -1 && state.isCodeSnippetMode) {
              // 发现代码片段结束标识
              state.isCodeSnippetMode = false
              // 显示结束标识后的内容，保留20个字符的缓冲
              const textAfterCode = state.streamingBuffer.substring(codeSnippetEndIndex + '</code_snippet>'.length)
              const displayableText = textAfterCode.length > 20
                ? textAfterCode.substring(0, textAfterCode.length - 20)
                : ''
              state.streamingDisplayText += displayableText
            } else if (!state.isCodeSnippetMode) {
              // 正常模式，保留20个字符的缓冲
              state.streamingDisplayText = state.streamingBuffer.length > 20
                ? state.streamingBuffer.substring(0, state.streamingBuffer.length - 20)
                : ''
            }
          })
        },

        // 设置代码片段模式
        setCodeSnippetMode: (isActive: boolean) => {
          set((state) => {
            state.isCodeSnippetMode = isActive
          })
        },

        // 完成流式传输
        completeStreaming: () => {
          set((state) => {
            // 移除<code_snippet>标签并显示所有内容
            const cleanedBuffer = state.streamingBuffer
              .replace(/<code_snippet>[\s\S]*?<\/code_snippet>/g, '')
            state.streamingDisplayText = cleanedBuffer
            state.isStreamActive = false
            state.isCodeSnippetMode = false

            // 延时500ms后刷新报告内容
            setTimeout(() => {
              get().refreshReportContent()
            }, 500)
          })
        },

        // 刷新报告内容
        refreshReportContent: async () => {
          const { currentTaskId } = get()
          if (currentTaskId) {
            try {
              // 重新加载消息列表
              await get().loadTaskMessages(currentTaskId)

              // 触发报告内容刷新事件
              const event = new CustomEvent('reportContentRefresh', {
                detail: { taskId: currentTaskId }
              })
              window.dispatchEvent(event)
            } catch (error) {
              console.error('Failed to refresh report content:', error)
            }
          }
        },

        // 重置流式传输状态
        resetStreamingState: () => {
          set((state) => {
            state.streamingBuffer = ''
            state.streamingDisplayText = ''
            state.isCodeSnippetMode = false
            state.isStreamActive = false
          })
        }
      }
    }),
    { name: 'ChatStore' }
  )
)
