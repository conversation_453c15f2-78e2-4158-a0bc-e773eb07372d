import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'

export interface ContextItem {
  id: string
  type: 'html_element' | 'html_content' | 'text' | 'image' | 'chart'
  tagName?: string
  className?: string
  elementId?: string
  content: string
  textContent?: string
  summary?: string
  timestamp: number
  source: 'selection' | 'manual' | 'auto'
}

export interface ContextStore {
  // 状态
  items: ContextItem[]
  maxItems: number
  
  // 操作
  addContext: (item: Omit<ContextItem, 'id' | 'timestamp'>) => void
  addMultipleContext: (items: Omit<ContextItem, 'id' | 'timestamp'>[]) => void
  removeContext: (id: string) => void
  clearContext: () => void
  updateContext: (id: string, updates: Partial<ContextItem>) => void
  
  // 查询
  getContextByType: (type: ContextItem['type']) => ContextItem[]
  getRecentContext: (count?: number) => ContextItem[]
  getContextSummary: () => string
  getCombinedHtmlContent: () => string
}

export const useContextStore = create<ContextStore>()(
  devtools(
    persist(
      immer((set, get) => ({
        // 初始状态
        items: [],
        maxItems: 50, // 最多保存50个上下文项

        // 添加单个上下文
        addContext: (item) => {
          set((state) => {
            const newItem: ContextItem = {
              ...item,
              id: `ctx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
              timestamp: Date.now()
            }
            
            state.items.unshift(newItem)
            
            // 保持最大数量限制
            if (state.items.length > state.maxItems) {
              state.items = state.items.slice(0, state.maxItems)
            }
          })
        },

        // 批量添加上下文
        addMultipleContext: (items) => {
          set((state) => {
            const newItems: ContextItem[] = items.map(item => ({
              ...item,
              id: `ctx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
              timestamp: Date.now()
            }))
            
            state.items.unshift(...newItems)
            
            // 保持最大数量限制
            if (state.items.length > state.maxItems) {
              state.items = state.items.slice(0, state.maxItems)
            }
          })
        },

        // 移除上下文
        removeContext: (id) => {
          set((state) => {
            state.items = state.items.filter(item => item.id !== id)
          })
        },

        // 清空所有上下文
        clearContext: () => {
          set((state) => {
            state.items = []
          })
        },

        // 更新上下文
        updateContext: (id, updates) => {
          set((state) => {
            const index = state.items.findIndex(item => item.id === id)
            if (index !== -1) {
              Object.assign(state.items[index], updates)
            }
          })
        },

        // 按类型获取上下文
        getContextByType: (type) => {
          return get().items.filter(item => item.type === type)
        },

        // 获取最近的上下文
        getRecentContext: (count = 10) => {
          return get().items.slice(0, count)
        },

        // 获取上下文摘要
        getContextSummary: () => {
          const items = get().items
          if (items.length === 0) return '暂无上下文内容'

          const summary = items.slice(0, 5).map(item => {
            const preview = item.textContent || item.content
            return `${item.type}: ${preview.substring(0, 100)}...`
          }).join('\n')

          return `当前有 ${items.length} 个上下文项:\n${summary}`
        },

        // 获取合并的HTML内容（用于聊天上下文）
        getCombinedHtmlContent: () => {
          const items = get().items
          if (items.length === 0) return ''

          // 只获取HTML类型的内容
          const htmlItems = items.filter(item =>
            item.type === 'html_element' || item.type === 'html_content'
          )

          if (htmlItems.length === 0) return ''

          // 合并所有HTML内容
          return htmlItems.map(item => item.content).join('\n')
        }
      })),
      {
        name: 'context-store',
        partialize: (state) => ({
          items: state.items.slice(0, 20), // 只持久化最近20个
          maxItems: state.maxItems
        })
      }
    ),
    { name: 'ContextStore' }
  )
)
