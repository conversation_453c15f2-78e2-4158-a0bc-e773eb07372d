# 中文URL编码问题修复方案

## 问题描述

前端请求时对URL的编码中可能有中文，比如 `/api/tasks/%E4%BB%BB%E5%8A%A1%20report_generation_test`，但是后端对其的解析方式与前端URL中中文的编码方式不一致，导致参数获取失败。

## 问题分析

1. **前端编码**：JavaScript使用 `encodeURIComponent()` 将中文字符编码为UTF-8的百分号编码
2. **后端解码**：需要使用 `urllib.parse.unquote()` 正确解码UTF-8编码的URL参数
3. **WebSocket路由缺失**：原本缺少WebSocket路由处理，导致WebSocket连接失败

## 解决方案

### 1. 后端修复

#### 1.1 创建WebSocket路由文件
创建 `backend/app/api/websocket.py`，包含：
- WebSocket路由处理
- URL参数解码逻辑
- 消息处理逻辑

```python
@websocket_router.websocket("/ws/chat/{task_id}")
async def websocket_chat(websocket: WebSocket, task_id: str):
    # URL解码任务ID
    decoded_task_id = urllib.parse.unquote(task_id)
    # ... 其他处理逻辑
```

#### 1.2 注册WebSocket路由
在 `backend/main.py` 中注册WebSocket路由：

```python
from app.api.websocket import websocket_router
app.include_router(websocket_router)
```

#### 1.3 API路由解码
在 `backend/app/api/routes.py` 中，所有涉及 `task_id` 参数的路由都已使用 `urllib.parse.unquote()` 解码。

### 2. 前端修复

#### 2.1 任务服务API编码
在 `frontend/src/services/api/taskService.ts` 中：

```typescript
async getTask(taskId: string): Promise<APIResponse<TaskDetail>> {
  const encodedTaskId = encodeURIComponent(taskId)
  return this.get<APIResponse<TaskDetail>>(`/api/tasks/${encodedTaskId}`)
}
```

#### 2.2 聊天服务API编码
在 `frontend/src/services/http/chatService.ts` 中：

```typescript
const encodedTaskId = encodeURIComponent(this.currentTaskId)
const response = await this.post<APIResponse<any>>(
  `/api/tasks/${encodedTaskId}/messages`,
  // ...
)
```

#### 2.3 消息服务API编码
在 `frontend/src/services/api/messageService.ts` 中：

```typescript
sendMessage: async (taskId: string, content: string, contextHtml?: string): Promise<Message> => {
  const encodedTaskId = encodeURIComponent(taskId)
  const response = await apiClient.post(`/api/tasks/${encodedTaskId}/messages`, {
    // ...
  })
}
```

## 修复内容总结

### 后端修复
1. ✅ 创建了WebSocket路由 (`backend/app/api/websocket.py`)
2. ✅ 在WebSocket路由中添加了URL解码逻辑
3. ✅ 在所有API路由中确保了URL解码逻辑
4. ✅ 注册了WebSocket路由到主应用

### 前端修复
1. ✅ 在任务服务中添加了URL编码逻辑
2. ✅ 在聊天服务中添加了URL编码逻辑
3. ✅ 在消息服务中添加了URL编码逻辑

## 测试验证

运行测试脚本验证修复效果：

```bash
# 基础URL编码解码测试
python tests/test_url_encoding.py

# 完整修复验证测试
python tests/test_chinese_url_fix.py
```

## 编码标准

- **前端编码**：使用 `encodeURIComponent()` 编码包含中文的任务ID
- **后端解码**：使用 `urllib.parse.unquote()` 解码URL参数
- **字符编码**：确保前后端使用相同的UTF-8编码标准

## 支持的字符

修复后的方案支持以下字符类型：
- ✅ 中文字符：`任务`、`测试`
- ✅ 英文字符：`task`、`report`
- ✅ 数字：`123456`
- ✅ 特殊字符：`!@#$%^&*()`
- ✅ 空格：`任务 report_generation_test`
- ✅ 下划线：`task_123_abc`
- ✅ Emoji：`😀🎉`

## 示例

### 问题场景
```
原始任务ID: "任务 report_generation_test"
前端URL: /api/tasks/%E4%BB%BB%E5%8A%A1%20report_generation_test
后端接收: %E4%BB%BB%E5%8A%A1%20report_generation_test
后端解码: "任务 report_generation_test" ✅
```

### WebSocket连接
```
WebSocket URL: ws://localhost:8088/ws/chat/%E4%BB%BB%E5%8A%A1%20report_generation_test
后端解码: "任务 report_generation_test" ✅
```

## 注意事项

1. **一致性**：确保前端编码和后端解码使用相同的标准
2. **防御性编程**：即使FastAPI会自动解码，手动解码可以确保兼容性
3. **测试覆盖**：对包含中文的任务ID进行充分测试
4. **错误处理**：在解码失败时提供适当的错误信息

## 相关文件

### 后端文件
- `backend/app/api/websocket.py` - WebSocket路由处理
- `backend/app/api/routes.py` - API路由处理
- `backend/main.py` - 主应用配置

### 前端文件
- `frontend/src/services/api/taskService.ts` - 任务API服务
- `frontend/src/services/http/chatService.ts` - 聊天HTTP服务
- `frontend/src/services/api/messageService.ts` - 消息API服务

### 测试文件
- `tests/test_url_encoding.py` - URL编码解码测试
- `tests/test_chinese_url_fix.py` - 完整修复验证测试
